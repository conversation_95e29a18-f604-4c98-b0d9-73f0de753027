[versions]
adjustAndroid = "5.4.1"
firebaseBom = "34.1.0"
installreferrer = "2.2"
playServicesAdsIdentifier = "18.2.0"
review = "2.0.2"
reviewKtx = "2.0.2"
thinkinganalyticssdk = "3.0.2"
versionCode = "15"
versionName = "1.0.9"

agp = "8.11.1"
googleServices = "4.4.3"
crashlytics = "3.0.6"
alertKmpAndroid = "2.0.0"
cosAndroidLite = "5.9.46"
kotlin = "2.2.0"
kotlinxDatetime = "0.7.1"
ktlintGradle = "5.2.0"  # 这是一个稳定且常用的 kotlinter-gradle 插件版本
composeRules = "0.4.26"  # 这是与上面版本兼容的 compose-rules 版本
coreKtx = "1.17.0"
media3 = "1.8.0"
splashscreen = "1.2.0-beta02"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
lifecycleRuntimeKtx = "2.9.2"
lifecycleProcess = "2.7.0"
composeBom = "2025.08.01"
coil = "3.3.0"
retrofit = "3.0.0"
retrofitConverter = "3.0.0"
okhttp = "5.1.0"
nav3Core = "1.0.0-alpha08"
material3AdaptiveNav3 = "1.0.0-SNAPSHOT"
lifecycleViewmodelNav3 = "1.0.0-alpha04"
kotlinxSerializationJson = "1.9.0"
mmkv = "2.2.2"
timber = "5.0.1"
credentials = "1.5.0"
googleid = "1.1.1"
imsdkPlus = "8.6.7040"
imUiCore = "8.6.7021"
imPush = "8.6.7019"
googleBilling = "8.0.0"
ksp = "2.2.0-2.0.2"
room = "2.7.2"
yyeva = "1.1.60"
appcompat = "1.7.1"
accompanistCompose = "0.37.3"

tencentVoiceRtc = "12.7.0.19072"
jsbridge = "1.0.7"
zoomable = "0.16.0"
webkitVersion = "1.14.0"

# KSP Processor dependencies
asm = "9.7"
kotlinpoet = "1.18.1"
autoService = "1.1.1"

[libraries]
adjust-android = { module = "com.adjust.sdk:adjust-android", version.ref = "adjustAndroid" }
alert-kmp-android = { module = "io.github.khubaibkhan4:alert-kmp-android", version.ref = "alertKmpAndroid" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
#androidx-core-splashscreen = { group = "androidx.core", name = "core-splashscreen", version.ref = "splashscreen" }
androidx-media3-exoplayer-hls = { module = "androidx.media3:media3-exoplayer-hls", version.ref = "media3" }
androidx-media3-ui-compose = { module = "androidx.media3:media3-ui-compose", version.ref = "media3" }
androidx-media3-exoplayer = { module = "androidx.media3:media3-exoplayer", version.ref = "media3" }

firebase-bom = { module = "com.google.firebase:firebase-bom", version.ref = "firebaseBom" }
firebase-analytics = { group = "com.google.firebase", name = "firebase-analytics" }
firebase-crashlytics = { group = "com.google.firebase", name = "firebase-crashlytics" }
installreferrer = { module = "com.android.installreferrer:installreferrer", version.ref = "installreferrer" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-lifecycle-process = { group = "androidx.lifecycle", name = "lifecycle-process", version.ref = "lifecycleRuntimeKtx" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose" }
androidx-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-material3 = { group = "androidx.compose.material3", name = "material3" }

coil-compose = { group = "io.coil-kt.coil3", name = "coil-compose", version.ref = "coil" }
coil-okhttp = { group = "io.coil-kt.coil3", name = "coil-network-okhttp", version.ref = "coil" }
coil-gif = { group = "io.coil-kt.coil3", name = "coil-gif", version.ref = "coil" }
coil-video = { group = "io.coil-kt.coil3", name = "coil-video", version.ref = "coil" }

kotlin-reflect = { module = "org.jetbrains.kotlin:kotlin-reflect", version.ref = "kotlin" }
kotlinx-datetime = { module = "org.jetbrains.kotlinx:kotlinx-datetime", version.ref = "kotlinxDatetime" }
play-services-ads-identifier = { module = "com.google.android.gms:play-services-ads-identifier", version.ref = "playServicesAdsIdentifier" }
retrofit-core = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofit" }
retrofit-converter = { group = "com.squareup.retrofit2", name = "converter-kotlinx-serialization", version.ref = "retrofitConverter" }
okhttp-core = { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okhttp" }
okhttp-logging = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okhttp" }
androidx-navigation3-runtime = { group = "androidx.navigation3", name = "navigation3-runtime", version.ref = "nav3Core" }
androidx-navigation3-ui = { group = "androidx.navigation3", name = "navigation3-ui", version.ref = "nav3Core" }
#androidx-material3-adaptive-navigation3 = { group = "androidx.compose.material3.adaptive", name = "adaptive-navigation3", version.ref = "material3AdaptiveNav3" }
androidx-lifecycle-viewmodel-navigation3 = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-navigation3", version.ref = "lifecycleViewmodelNav3" }
kotlinx-serialization-json = { group = "org.jetbrains.kotlinx", name = "kotlinx-serialization-json", version.ref = "kotlinxSerializationJson" }

accompanist-drawablepainter = { module = "com.google.accompanist:accompanist-drawablepainter", version.ref = "accompanistCompose" }

review = { module = "com.google.android.play:review", version.ref = "review" }
review-ktx = { module = "com.google.android.play:review-ktx", version.ref = "reviewKtx" }
tencent-mmkv = { group = "com.tencent", name = "mmkv", version.ref = "mmkv" }
thinkinganalyticssdk = { module = "cn.thinkingdata.android:ThinkingAnalyticsSDK", version.ref = "thinkinganalyticssdk" }
timber = { group = "com.jakewharton.timber", name = "timber", version.ref = "timber" }
ktlint-compose-rules = { module = "io.nlopez.compose.rules:ktlint", version.ref = "composeRules" }

androidx-credentials = { module = "androidx.credentials:credentials", version.ref = "credentials" }
androidx-credentials-play-services-auth = { module = "androidx.credentials:credentials-play-services-auth", version.ref = "credentials" }
googleid = { module = "com.google.android.libraries.identity.googleid:googleid", version.ref = "googleid" }

imsdk-plus = { module = "com.tencent.imsdk:imsdk-plus", version.ref = "imsdkPlus" }
timquic-plugin = { module = "com.tencent.imsdk:timquic-plugin", version.ref = "imsdkPlus" }
timui-core = { module = "com.tencent.liteav.tuikit:tuicore", version.ref = "imUiCore" }
timpush = { module = "com.tencent.timpush:timpush", version.ref = "imPush" }
timpush-fcm = { module = "com.tencent.timpush:fcm", version.ref = "imPush" }
tencent-rtc = { module = "com.tencent.liteav:LiteAVSDK_TRTC", version.ref = "tencentVoiceRtc" }
cos-android-lite = { module = "com.qcloud.cos:cos-android-lite", version.ref = "cosAndroidLite" }

# room
androidx-room-compiler = { module = "androidx.room:room-compiler", version.ref = "room" }
androidx-room-ktx = { module = "androidx.room:room-ktx", version.ref = "room" }
androidx-room-runtime = { module = "androidx.room:room-runtime", version.ref = "room" }

# google billing
google-billing = { module = "com.android.billingclient:billing", version.ref = "googleBilling" }
google-billing-ktx = { module = "com.android.billingclient:billing-ktx", version.ref = "googleBilling" }

jsbridge = { module = "com.smallbuer:jsbridge", version.ref = "jsbridge" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }

# yyeva
yyeva = { module = "com.github.yylive.YYEVA-Android:yyeva", version.ref = "yyeva" }
zoomable-peek-overlay = { module = "me.saket.telephoto:zoomable-peek-overlay", version.ref = "zoomable" }
zoomable = { module = "me.saket.telephoto:zoomable", version.ref = "zoomable" }
zoomable-image-coil3 = { module = "me.saket.telephoto:zoomable-image-coil3", version.ref = "zoomable" }
webkit = { group = "androidx.webkit", name = "webkit", version.ref = "webkitVersion" }

# KSP Processor libraries
asm-core = { module = "org.ow2.asm:asm", version.ref = "asm" }
asm-commons = { module = "org.ow2.asm:asm-commons", version.ref = "asm" }
asm-util = { module = "org.ow2.asm:asm-util", version.ref = "asm" }
kotlinpoet-core = { module = "com.squareup:kotlinpoet", version.ref = "kotlinpoet" }
kotlinpoet-ksp = { module = "com.squareup:kotlinpoet-ksp", version.ref = "kotlinpoet" }
auto-service = { module = "com.google.auto.service:auto-service", version.ref = "autoService" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
jetbrains-kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
ktlint-gradle = { id = "org.jmailen.kotlinter", version.ref = "ktlintGradle" }
devtools-ksp = { id = "com.google.devtools.ksp",  version.ref = "ksp"  }
android-library = { id = "com.android.library", version.ref = "agp" }
google-services = { id = "com.google.gms.google-services", version.ref = "googleServices" }
firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "crashlytics" }
