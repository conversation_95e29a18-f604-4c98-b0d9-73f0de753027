import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.konan.properties.Properties
import org.jetbrains.kotlin.konan.properties.loadProperties
import java.text.SimpleDateFormat
import java.util.Date

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
    alias(libs.plugins.jetbrains.kotlin.serialization)
    alias(libs.plugins.ktlint.gradle)
    alias(libs.plugins.devtools.ksp)
    alias(libs.plugins.google.services)
    alias(libs.plugins.firebase.crashlytics)
    id("com.buque.env")
}

val localProperties: Properties = loadProperties(rootProject.file("local.properties").path)
ksp {
    arg("room.incremental", "true")
    arg("room.schemaLocation", "$projectDir/schemas")
}
android {
    namespace = "com.buque.wakoo"
    compileSdk = 36

    defaultConfig {
        applicationId = "com.site.wakoo"
        minSdk = 28
        targetSdk = 35
        versionCode = (System.getenv()["VERSION_CODE"] ?: libs.versions.versionCode.get()).toInt()
        versionName = System.getenv()["BUILD_VERSION_NAME"] ?: libs.versions.versionName.get()

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        create("test") {
            storeFile = file("../app-test.jks")
            storePassword = "12345678"
            keyAlias = "test"
            keyPassword = "12345678"
        }
        create("release") {
            storeFile = file("../app.jks")
            storePassword = localProperties.getProperty("SIGNING_PWD")
            keyAlias = localProperties.getProperty("SIGNING_ALIAS")
            keyPassword = localProperties.getProperty("SIGNING_PWD")
        }
    }

    buildTypes {

        debug {
            // 是否可调试：允许连接调试器
            isDebuggable = true
            applicationIdSuffix = ".debug"

            // 代码和资源优化：通常不进行混淆和资源压缩，方便调试
            isMinifyEnabled = false
            isShrinkResources = false

            manifestPlaceholders["appLabel"] = "wakoo-debug"

            signingConfig = signingConfigs.getByName("test")

//            configure<CrashlyticsExtension> {
//                mappingFileUploadEnabled = false
//            }

            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro",
            )
        }

        release {
            // 是否可调试：禁用，生产环境不可调试
            isDebuggable = false

            // 代码和资源优化：启用混淆、资源压缩，减小包体积，提高安全性
            isMinifyEnabled = true // 启用代码和资源混淆/优化
            isShrinkResources = true // 移除未使用的资源文件

            manifestPlaceholders["appLabel"] = "wakoo"

            signingConfig = signingConfigs.getByName("release")

//            configure<CrashlyticsExtension> {
//                mappingFileUploadEnabled = true
//            }
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro",
            )
        }

        create("profile") {
            initWith(getByName("release"))
            applicationIdSuffix = ".profile"

            manifestPlaceholders["appLabel"] = "wakoo-profile"

            // isDebuggable = true // 如果需要旧设备上的性能分析，但会降低真实性
            isProfileable = true // 推荐：用于 Android 10+ 设备的低开销性能分析

            signingConfig = signingConfigs.getByName("test")
//
//            configure<CrashlyticsExtension> {
//                mappingFileUploadEnabled = false
//            }

            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro",
            )
        }
    }

    flavorDimensions += "environment"
    flavorDimensions += "channel"

    productFlavors {
        create("play") {
            dimension = "channel"
            ndk {
                abiFilters += listOf("armeabi-v7a", "arm64-v8a")
            }
        }

        create("official") {
            dimension = "channel"
            ndk {
                abiFilters += listOf("armeabi-v7a", "arm64-v8a", "x86_64")
            }
        }

        create("development") {
            dimension = "environment"
        }

        create("production") {
            dimension = "environment"
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlin {
        compilerOptions {
            freeCompilerArgs.add("-Xcontext-parameters")
            freeCompilerArgs.add("-opt-in=androidx.compose.material3.ExperimentalMaterial3Api")
            jvmTarget.set(JvmTarget.JVM_11)
        }
    }

    buildFeatures {
        compose = true
        buildConfig = true
    }
    kotlinter {
        ignoreFormatFailures = true
        ignoreLintFailures = false
        reporters = arrayOf("checkstyle")
    }

    packaging {
        jniLibs.pickFirsts.add("**/libc++_shared.so")
    }

    fun getCurrentTime(): String {
        val sdf = SimpleDateFormat("MMddHHmm")
        return sdf.format(Date())
    }

    // 只在Release版本时设置archivesBaseName，避免影响增量编译
    gradle.taskGraph.whenReady {
        val releaseTaskPatterns =
            listOf(
                "assembleProductionPlayRelease",
                "assembleProductionOfficialRelease",
                "bundleProductionPlayRelease",
                "bundleProductionOfficialRelease",
            )

        val hasReleaseTask =
            releaseTaskPatterns.any { pattern ->
                gradle.taskGraph.hasTask(":app:$pattern")
            }

        if (hasReleaseTask) {
            setProperty(
                "archivesBaseName",
                "${project.name}-${defaultConfig.versionName}.${defaultConfig.versionCode}_${getCurrentTime()}",
            )
        }
    }
}

dependencies {

    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar", "*.aar"))))

    implementation(libs.androidx.core.ktx)
//    implementation(libs.androidx.core.splashscreen)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.lifecycle.process)
    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material3)

    // Coil - 图片加载库
    implementation(libs.coil.compose)
    implementation(libs.coil.okhttp)
    implementation(libs.coil.gif)
    implementation(libs.coil.video)

    // Retrofit & OkHttp - 网络请求
    implementation(libs.retrofit.core)
    implementation(libs.retrofit.converter)
    implementation(libs.okhttp.core)
    implementation(libs.okhttp.logging)

    // navigation3
    implementation(libs.androidx.navigation3.runtime)
    implementation(libs.androidx.navigation3.ui)
    implementation(libs.androidx.lifecycle.viewmodel.navigation3)
//    implementation(libs.androidx.material3.adaptive.navigation3)
    implementation(libs.kotlinx.serialization.json)

    // mmkv
    implementation(libs.tencent.mmkv)

    // 日志框架
    implementation(libs.timber)

    // 支付
    implementation(libs.google.billing)
    implementation(libs.google.billing.ktx)

    // Google Login
    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.analytics)
    implementation(libs.firebase.crashlytics)
    implementation(libs.androidx.credentials)
    implementation(libs.androidx.credentials.play.services.auth)
    implementation(libs.googleid)
    api(project(":lib-webview"))
    implementation(libs.webkit)

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)
    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)

    // Tencent IM
    implementation(libs.imsdk.plus)
    implementation(libs.timquic.plugin)
    implementation(libs.timui.core)
    implementation(libs.timpush) {
        // 排除glide, 可能存在问题, 腾讯的推送用到了glide
        exclude("com.github.bumptech.glide")
    }
    implementation(libs.tencent.rtc)
    //    implementation(libs.timpush.fcm)

    implementation(libs.kotlin.reflect)
    implementation(libs.zoomable.image.coil3)
    implementation(libs.zoomable)
    implementation(libs.zoomable.peek.overlay)
    implementation(libs.androidx.media3.ui.compose)
    implementation(libs.androidx.media3.exoplayer)
    implementation(libs.androidx.media3.exoplayer.hls)
    implementation(libs.alert.kmp.android)
    implementation(libs.kotlinx.datetime)
    implementation(libs.cos.android.lite)

    implementation(libs.accompanist.drawablepainter)

    // 评分
    implementation(libs.review)
    implementation(libs.review.ktx)

    // adjust
    implementation(libs.adjust.android)
    implementation(libs.installreferrer)
    implementation(libs.play.services.ads.identifier)

    // room数据库
    implementation(libs.androidx.room.runtime)
    implementation(libs.androidx.room.ktx)
    ksp(libs.androidx.room.compiler)

    ktlint(libs.ktlint.compose.rules)

    implementation(libs.yyeva)

    implementation(libs.thinkinganalyticssdk)
}
