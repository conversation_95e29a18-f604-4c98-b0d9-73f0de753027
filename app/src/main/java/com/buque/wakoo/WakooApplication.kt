package com.buque.wakoo

import android.annotation.SuppressLint
import android.app.Application
import android.content.Context
import android.content.res.Configuration
import coil3.ImageLoader
import coil3.SingletonImageLoader
import coil3.gif.AnimatedImageDecoder
import coil3.request.crossfade
import coil3.video.VideoFrameDecoder
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.core.pay.GoogleBillingManager
import com.buque.wakoo.core.webview.AppLinkNavigator
import com.buque.wakoo.core.webview.WebBridgeHandler
import com.buque.wakoo.core.webview.offline.OfflinePkgManager
import com.buque.wakoo.im_business.IMBusinessCore
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.DeviceInfoManager
import com.buque.wakoo.manager.DownloadManager
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.manager.L10nManager
import com.buque.wakoo.manager.NetworkManager
import com.buque.wakoo.network.api.service.SettingsApiService
import com.buque.wakoo.repository.GlobalRepository
import com.buque.wakoo.utils.FileUtils
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.ReflectionUtils
import com.buque.wakoo.utils.datapoints.ShushuUtils
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import com.google.firebase.crashlytics.setCustomKeys
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber

class WakooApplication : Application() {
    companion object {
        @SuppressLint("StaticFieldLeak")
        internal lateinit var instance: Context

        internal fun attach(context: Context) {
            instance = context as? Application ?: context.applicationContext as? Application ?: context
        }
    }

    override fun attachBaseContext(base: Context?) {
        attach(this)
        super.attachBaseContext(base)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        L10nManager.onConfigurationChanged()
    }

    override fun onCreate() {
        super.onCreate()

        // 反射改变默认值
//        ReflectionUtils.modifyPrivateValue("androidx.compose.material3.TabKt", "HorizontalTextPadding", 18.dp.value)
        ReflectionUtils.modifyPrivateValue(
            className = "androidx.compose.material3.TabRowKt",
            fieldName = "ScrollableTabRowMinimumTabWidth",
            value = 0f,
        )
        // 初始化mmkv
        MMKV.initialize(this)
        // 环境配置
        EnvironmentManager.initialize()

        // 初始化Timber
        if (EnvironmentManager.current.enableLog) {
            // Debug版本：显示完整日志
            LogUtils.init(
                tree = Timber.DebugTree(),
                showThreadInfo = false,
                showCallerInfo = true,
            )
        } else {
            // Release版本：过滤日志并上报崩溃
            LogUtils.init(
                tree = ReleaseTree(),
                showThreadInfo = false,
                showCallerInfo = false,
            )
        }
        // 网络监听初始化
        NetworkManager.initialize(this)
        // 用户信息
        DeviceInfoManager.initialize(this)
        // 初始化IM
        IMBusinessCore.init(this)
        // 初始化账户管理，读取已登录信息
        AccountManager.initialize(this)
        // 初始化下载器
        DownloadManager.initialize(this)
        // 初始化创建一些目录
        FileUtils.initialize(this)
        // billing init
        GoogleBillingManager.initGoogleBilling(GlobalRepository.walletRepo)
        SettingsApiService.preload()
        // webJsBridge 初始化
        WebBridgeHandler.initialize()
        OfflinePkgManager.startCheck(this)
        // 初始化图片库
        initializeCoil()

        ShushuUtils.init(this)

        Firebase.crashlytics.setCustomKeys {
            key("environment", BuildConfig.FLAVOR_environment) // String value
            key("channel", BuildConfig.FLAVOR_channel) // String value
            key("git_commit_hash", BuildConfig.GIT_COMMIT_HASH) // String value
            key("version_code", BuildConfig.VERSION_CODE) // String value
        }

        // 写的问题很大，AppLinkNavigator的耗时操作放在了初始化器，并不是initialize方法
        appCoroutineScope.launch(Dispatchers.Default) {
            AppLinkNavigator.initialize()
        }
    }

    /**
     * Release版本使用的日志树，过滤低级别日志，只记录警告和错误
     * 可以集成崩溃报告系统
     */
    private class ReleaseTree : Timber.Tree() {
        override fun log(
            priority: Int,
            tag: String?,
            message: String,
            t: Throwable?,
        ) {
            if (priority >= android.util.Log.WARN || EnvironmentManager.current.enableLog) {
                // 这里可以将日志发送到崩溃分析服务
                // 例如：Crashlytics.log(priority, tag, message)
                // 如果有异常：Crashlytics.logException(t)

                // 保留系统日志输出，实际发布时可以移除
                android.util.Log.println(
                    priority,
                    tag,
                    message,
                )
            }
        }
    }

    private fun initializeCoil() {
        SingletonImageLoader.setSafe {
            ImageLoader
                .Builder(this)
                .crossfade(true)
                .components {
                    add(AnimatedImageDecoder.Factory())
                    add(VideoFrameDecoder.Factory())
                }.build()
        }
    }
}
