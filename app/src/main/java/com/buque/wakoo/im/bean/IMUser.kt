package com.buque.wakoo.im.bean

import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.ChatBubble
import com.buque.wakoo.bean.user.IUserDecorations
import com.buque.wakoo.bean.user.Medal
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.bean.user.UserDecorations
import com.buque.wakoo.network.api.bean.ExpLevelInfo
import com.buque.wakoo.network.api.bean.UserResponse
import kotlinx.serialization.EncodeDefault
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

data class IMUser(
    val user: User,
    val decorations: UserDecorations = UserDecorations.empty, // 装扮
) : User by user,
    IUserDecorations by decorations {
    companion object {
        fun fromResponse(response: UserResponse): IMUser =
            IMUser(
                user = BasicUser.fromResponse(response),
                decorations = UserDecorations.fromResponse(response),
            )

        fun fromUid(uid: String): IMUser = IMUser(BasicUser.fromUid(uid))
    }
}

@Deprecated("一般情况不要使用")
@Serializable
data class SendIMUser(
    @SerialName("userid") val id: String = "",
    @SerialName("nickname") val name: String = "",
    @SerialName("avatar_url") val avatar: String = "",
    @SerialName("gender") val gender: Int = 1,
    @SerialName("is_member") val isVip: Boolean = false,
    @SerialName("public_cp") val publicCP: BasicUser? = null,
    @SerialName("avatar_frame") val avatarFrame: String? = null,
    @SerialName("medal_list") val medalList: List<Medal> = emptyList(),
    @SerialName("level") val level: Int = 0,
    @SerialName("age") val age: Int = 18,
    @SerialName("small_img_url") val cpUrl: String? = null,
    @SerialName("chat_bubble") val bubble: ChatBubble? = null,
    @SerialName("country_flag") val countryFlag: String = "",
    @EncodeDefault @SerialName("exp_level_info") val expLevelInfo: ExpLevelInfo = ExpLevelInfo.Empty,
    @SerialName("colorfulNicknameGradient") val colorfulNicknameGradient: List<String>? = null,
) {
    fun toIMUser(): IMUser =
        IMUser(
            user =
                BasicUser(
                    id = id,
                    name = name,
                    avatar = avatar,
                    gender = gender,
                    age = age,
                    isVip = isVip,
                ),
            decorations =
                UserDecorations(
                    avatarFrame = avatarFrame,
                    medalList =
                        if (id == SelfUser?.id) {
                            medalList
                        } else {
                            medalList
                                .filter {
                                    it.type == 0 || it.type == 3
                                }.takeIf { it.isNotEmpty() }
                        },
                    haveVip = isVip,
                    level = level,
                    chatBubble = bubble,
                    charmLevel = expLevelInfo.charmLevel,
                    wealthLevel = expLevelInfo.wealthLevel,
                    cpUrl = cpUrl,
                    cpAvatar = publicCP?.avatar,
                    colorfulNicknameGradient = colorfulNicknameGradient.orEmpty(),
                ),
        )
}
