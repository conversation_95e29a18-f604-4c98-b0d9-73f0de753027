package com.buque.wakoo.im_business.tim

import android.content.Context
import android.os.Build
import androidx.collection.LruCache
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.bean.user.SelfUserInfo
import com.buque.wakoo.ext.parseValue
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.im.MessageBundle
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.UCMessage
import com.buque.wakoo.im.api.IMApi
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.bean.IMConversationResult
import com.buque.wakoo.im.bean.MessageReadReceipt
import com.buque.wakoo.im.bean.MsgSendCondition
import com.buque.wakoo.im.bean.MsgSendStatus
import com.buque.wakoo.im.bean.SendIMUser
import com.buque.wakoo.im.bean.SendParams
import com.buque.wakoo.im.compat.IIMEngine
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.compat.IMCompatCore.match
import com.buque.wakoo.im.exceptions.IMReLoginException
import com.buque.wakoo.im.inter.EventDispatcher
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im.inter.UCConversation
import com.buque.wakoo.im.push.IMNotificationHelper
import com.buque.wakoo.im.push.PushRouteHandler
import com.buque.wakoo.im.toPushInfo
import com.buque.wakoo.im.utils.IMLogUtils
import com.buque.wakoo.im.utils.IMUtils
import com.buque.wakoo.im.utils._isAppRelease
import com.buque.wakoo.im.utils.takeIsNotEmpty
import com.buque.wakoo.im_business.rtm.RtmManager
import com.buque.wakoo.im_business.tim.TIMConverter.convId2Id
import com.buque.wakoo.im_business.tim.TIMConverter.convertToMessage
import com.buque.wakoo.im_business.tim.TIMConverter.id2ConvId
import com.buque.wakoo.im_business.tim.TIMConverter.parseConvFromOrigin
import com.buque.wakoo.im_business.tim.TIMConverter.parseMsgFromOrigin
import com.buque.wakoo.im_business.tim.TIMConverter.toUCConversationListByTIM
import com.buque.wakoo.im_business.tim.TIMConverter.toUCInstanceMessage
import com.buque.wakoo.manager.AppManager
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.network.api.bean.ExpLevelInfo
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.network.onHttpFailure
import com.buque.wakoo.utils.SayHiLogicUtils
import com.tencent.imsdk.BaseConstants
import com.tencent.imsdk.conversation.ConversationAtInfo
import com.tencent.imsdk.v2.V2TIMAdvancedMsgListener
import com.tencent.imsdk.v2.V2TIMCallback
import com.tencent.imsdk.v2.V2TIMConversation
import com.tencent.imsdk.v2.V2TIMConversationListener
import com.tencent.imsdk.v2.V2TIMConversationOperationResult
import com.tencent.imsdk.v2.V2TIMConversationResult
import com.tencent.imsdk.v2.V2TIMGroupInfoResult
import com.tencent.imsdk.v2.V2TIMGroupListener
import com.tencent.imsdk.v2.V2TIMGroupMemberInfo
import com.tencent.imsdk.v2.V2TIMManager
import com.tencent.imsdk.v2.V2TIMMessage
import com.tencent.imsdk.v2.V2TIMMessageListGetOption
import com.tencent.imsdk.v2.V2TIMMessageReceipt
import com.tencent.imsdk.v2.V2TIMOfflinePushInfo
import com.tencent.imsdk.v2.V2TIMSDKConfig
import com.tencent.imsdk.v2.V2TIMSDKListener
import com.tencent.imsdk.v2.V2TIMSendCallback
import com.tencent.imsdk.v2.V2TIMUserFullInfo
import com.tencent.imsdk.v2.V2TIMValueCallback
import com.tencent.qcloud.tim.push.TIMPushCallback
import com.tencent.qcloud.tim.push.TIMPushListener
import com.tencent.qcloud.tim.push.TIMPushManager
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

@Serializable
data class ImageSize(
    val width: Int,
    val height: Int,
)

@Serializable
data class TimMsgExtensions(
    val data: Map<String, String> = emptyMap(),
    @SerialName("change_list") val changes: ChangeList? = null,
) {
    val addKeys: List<String>?
        get() = changes?.addKeys

    val deleteKeys: List<String>?
        get() = changes?.deleteKeys

    val updateKeys: List<String>?
        get() = changes?.updateKeys
}

@Serializable
data class ChangeList(
    @SerialName("add") val addKeys: List<String>? = null,
    @SerialName("del") val deleteKeys: List<String>? = null,
    @SerialName("update") val updateKeys: List<String>? = null,
)

internal fun V2TIMConversation.checkValid(ucType: ConversationType? = null) =
    when (type) {
        V2TIMConversation.V2TIM_C2C -> {
            ucType == null || ucType == ConversationType.C2C
        }

        V2TIMConversation.V2TIM_GROUP -> {
            when (groupType) {
                V2TIMManager.GROUP_TYPE_MEETING -> ucType == null || ucType == ConversationType.CHATROOM
                V2TIMManager.GROUP_TYPE_PUBLIC -> ucType == null || ucType == ConversationType.GROUP
                V2TIMManager.GROUP_TYPE_AVCHATROOM -> ucType == null || ucType == ConversationType.RTM
                else -> false
            }
        }

        else -> {
            false
        }
    }

object TIMEngine : IIMEngine {
    private var dispatcher: EventDispatcher? = null

    private val mUserId: String?
        get() = IMCompatCore.currentUser?.id

    private val sdkListener =
        object : V2TIMSDKListener() {
            override fun onUserSigExpired() {
                IMUtils.launchOnMain {
                    val ret =
                        try {
                            login(mUserId ?: "", null)
                        } catch (e: Exception) {
                            false
                        }
                    if (!ret) {
                        dispatcher?.synchronizedDispatchListener {
                            it.onClientOffline("Expired", false)
                        }
                    }
                }
            }

            override fun onKickedOffline() {
                dispatcher?.synchronizedDispatchListener {
                    it.onClientOffline("Kicked", true)
                }
            }
        }

    private val convListener =
        object : V2TIMConversationListener() {
            override fun onNewConversation(list: MutableList<V2TIMConversation>) {
                if (list.isEmpty()) {
                    return
                }
                IMUtils.launchOnUnMain {
                    val conversationList = list.toUCConversationListByTIM()
                    dispatcher?.dispatchListener {
                        it.onNewConversation(conversationList)
                    }
                }
            }

            override fun onConversationChanged(list: MutableList<V2TIMConversation>) {
                IMUtils.launchOnUnMain {
                    val conversationList = list.toUCConversationListByTIM()
                    dispatcher?.dispatchListener {
                        it.onConversationChanged(conversationList)
                    }
                }
            }

            override fun onConversationDeleted(conversationIDList: MutableList<String>) {
                val idList =
                    conversationIDList.map {
                        it.convId2Id()
                    }

                if (idList.isEmpty()) {
                    return
                }
                val ids = idList.toSet()
                dispatcher?.synchronizedDispatchListener {
                    it.onConversationDeleted(ids)
                }
            }
        }

    private val groupListener =
        object : V2TIMGroupListener() {
            override fun onMemberEnter(
                groupID: String,
                memberList: List<V2TIMGroupMemberInfo>,
            ) {
                dispatcher?.synchronizedDispatchListener {
                    it.onGroupMemberEnter(groupID, memberList)
                }
            }

            override fun onMemberLeave(
                groupID: String,
                member: V2TIMGroupMemberInfo,
            ) {
                dispatcher?.synchronizedDispatchListener {
                    it.onGroupMemberLeave(groupID, member)
                }
            }

            override fun onMemberKicked(
                groupID: String,
                opUser: V2TIMGroupMemberInfo,
                memberList: List<V2TIMGroupMemberInfo>,
            ) {
                dispatcher?.synchronizedDispatchListener {
                    it.onGroupMemberKicked(groupID, opUser, memberList)
                }
            }
        }

    private val pushListener =
        object : TIMPushListener() {
            override fun onNotificationClicked(ext: String?) {
                PushRouteHandler.handle(ext)
            }
        }

    private val mV2TIMAdvancedMsgListener =
        object : V2TIMAdvancedMsgListener() {
            override fun onRecvNewMessage(msg: V2TIMMessage) {
                IMLogUtils.i("receive new Msg:$msg")
                IMUtils.launchOnUnMain {
                    val ucMessage = parseMsgFromOrigin(msg).toUCInstanceMessage()
                    dispatcher?.dispatchRecvNewMessage(ucMessage)
                }
            }

            override fun onRecvC2CReadReceipt(receiptList: MutableList<V2TIMMessageReceipt>) {
                if (receiptList.isEmpty()) {
                    return
                }
                val readReceiptMap = hashMapOf<String, Long>()
                receiptList.forEach { item ->
                    val userId = item.userID
                    if (!userId.isNullOrEmpty()) {
                        item.timestamp.takeIf { it > 0 }?.also {
                            if (readReceiptMap.containsKey(userId)) {
                                readReceiptMap[userId] = maxOf(readReceiptMap[userId] ?: 0, it.times(1000))
                            } else {
                                readReceiptMap[userId] = it.times(1000)
                            }
                        }
                    }
                }

                if (readReceiptMap.isNotEmpty()) {
                    val readReceipts =
                        buildList {
                            readReceiptMap.forEach { (key, value) ->
                                add(MessageReadReceipt.Full(key, value))
                            }
                        }
                    dispatcher?.synchronizedDispatchListener {
                        if (it.filter.isEmpty) {
                            it.onReadReceiptsReceived(readReceipts)
                        } else {
                            readReceipts.forEach { item ->
                                if (item.targetId == it.filter.id) {
                                    it.onReadReceiptReceived(item)
                                }
                            }
                        }
                    }
                }
            }

            // 消息已读回执通知
            override fun onRecvMessageReadReceipts(receiptList: List<V2TIMMessageReceipt>) = Unit

            // 收到消息撤回的通知
            override fun onRecvMessageRevoked(
                messageID: String,
                operateUser: V2TIMUserFullInfo,
                reason: String,
            ) {
                IMUtils.launchOnMain {
                    delay(150) // 这里做延迟查询，是因为tim sdk有bug，立刻查询消息并不是撤回状态
                    V2TIMManager
                        .getMessageManager()
                        .findMessages(
                            listOf(messageID),
                            object : V2TIMValueCallback<List<V2TIMMessage>> {
                                override fun onSuccess(messages: List<V2TIMMessage>) {
                                    val message = messages.find { it.msgID == messageID }
                                    if (message != null) {
                                        IMUtils.launchOnUnMain {
                                            val ucMessage = parseMsgFromOrigin(message).toUCInstanceMessage()
                                            dispatcher?.dispatchListener {
                                                if (it.match(ucMessage)) {
                                                    it.onMessageRecalled(ucMessage)
                                                }
                                            }
                                        }
                                    }
                                }

                                override fun onError(
                                    code: Int,
                                    desc: String?,
                                ) = Unit
                            },
                        )
                }
            }

            // 消息内容被修改
            override fun onRecvMessageModified(message: V2TIMMessage) {
                IMUtils.launchOnUnMain {
                    val timMessage = parseMsgFromOrigin(message)
                    val ucMessage = timMessage.toUCInstanceMessage()

                    var addEntry: Map<String, String>? = null
                    var changeEntry: Map<String, String>? = null
                    var deleteEntry: Array<String>? = null

                    timMessage.extensions?.apply {
                        if (!addKeys.isNullOrEmpty()) {
                            addEntry =
                                buildMap {
                                    addKeys!!.forEach {
                                        put(it, data[it].orEmpty())
                                    }
                                }
                        }

                        if (!updateKeys.isNullOrEmpty()) {
                            changeEntry =
                                buildMap {
                                    updateKeys!!.forEach {
                                        put(it, data[it].orEmpty())
                                    }
                                }
                        }

                        if (!deleteKeys.isNullOrEmpty()) {
                            deleteEntry = deleteKeys!!.toTypedArray()
                        }
                    }

                    dispatcher?.dispatchListener(
                        {
                            IMCompatCore.putMsg(ucMessage)
                        },
                    ) {
                        if (it.match(ucMessage)) {
                            it.onMessageModified(ucMessage)
                            if (addEntry != null) {
                                it.onMessageExpansionUpdate(ucMessage, true, addEntry)
                            }
                            if (changeEntry != null) {
                                it.onMessageExpansionUpdate(ucMessage, false, changeEntry)
                            }
                            if (deleteEntry != null) {
                                it.onMessageExpansionDelete(ucMessage, deleteEntry)
                            }
                        }
                    }
                }
            }
        }

    override fun initSDK(context: Context) {
        V2TIMManager.getInstance().initSDK(
            context,
            EnvironmentManager.current.tencentAppId,
            V2TIMSDKConfig().also {
                it.logLevel =
                    if (_isAppRelease) {
                        V2TIMSDKConfig.V2TIM_LOG_ERROR
                    } else {
                        V2TIMSDKConfig.V2TIM_LOG_DEBUG
                    }
            },
        )
        TIMPushManager.getInstance().apply {
            addPushListener(pushListener)
            disablePostNotificationInForeground(true)
            forceUseFCMPushChannel(true)
        }

        V2TIMManager.getInstance().addIMSDKListener(sdkListener)
        V2TIMManager.getMessageManager().addAdvancedMsgListener(mV2TIMAdvancedMsgListener)
        V2TIMManager.getConversationManager().addConversationListener(convListener)
        V2TIMManager.getInstance().addGroupListener(groupListener)

        val imCompatListener =
            object : IMCompatListener {
                override fun onRecvNewMessage(
                    message: UCInstanceMessage,
                    offline: Boolean,
                ) {
                    if (!IMNotificationHelper.hasNotificationPermission()) {
                        return
                    }
                    if (!hasFcmToken && !AppManager.appIsForeground) {
                        val timMessage = (message.base as TIMMessage).rawMessage
                        val title =
                            timMessage.offlinePushInfo
                                ?.title
                                .takeIsNotEmpty() ?: return
                        val desc =
                            timMessage.offlinePushInfo
                                ?.desc
                                .takeIsNotEmpty() ?: return
                        IMUtils.launchOnMain {
                            if (!message.isC2CMsg && !getGroupRecvOpt(message.receiver)) { // 群组开了免打扰
                                return@launchOnMain
                            }
                            if (!AppManager.appIsForeground) {
                                IMNotificationHelper.showIMMessageNotification(message, title, desc)
                            }
                        }
                    }
                }

                override fun onSendMessageResult(
                    message: UCInstanceMessage,
                    success: Boolean,
                ) {
                    if (!IMNotificationHelper.hasNotificationPermission()) {
                        return
                    }
                    if (success && !hasFcmToken && !AppManager.appIsForeground) {
                        val timMessage = (message.base as TIMMessage).rawMessage
                        val title =
                            timMessage.offlinePushInfo
                                ?.title
                                .takeIsNotEmpty() ?: return
                        val desc =
                            timMessage.offlinePushInfo
                                ?.desc
                                .takeIsNotEmpty() ?: return
                        IMUtils.launchOnMain {
                            if (!message.isC2CMsg && !getGroupRecvOpt(message.receiver)) { // 群组开了免打扰
                                return@launchOnMain
                            }
                            if (!AppManager.appIsForeground) {
                                IMNotificationHelper.showIMMessageNotification(message, title, desc)
                            }
                        }
                    }
                }

                override fun onMessageRecalled(message: UCInstanceMessage) {
                    if (!IMNotificationHelper.hasNotificationPermission()) {
                        return
                    }
                    if (!hasFcmToken && !AppManager.appIsForeground) {
                        val timMessage = (message.base as TIMMessage).rawMessage
                        val title =
                            timMessage.offlinePushInfo
                                ?.title
                                .takeIsNotEmpty() ?: return
                        val desc = "撤回了一条消息".localized
                        IMUtils.launchOnMain {
                            if (!message.isC2CMsg && !getGroupRecvOpt(message.receiver)) { // 群组开了免打扰
                                return@launchOnMain
                            }
                            if (!AppManager.appIsForeground) {
                                IMNotificationHelper.showIMMessageNotification(message, title, desc)
                            }
                        }
                    }
                }
            }
        IMCompatCore.addIMListener(imCompatListener)
    }

    override suspend fun login(
        userId: String,
        token: String?,
        loadConversation: (() -> Unit)?,
    ): Boolean {
        val imToken = token ?: TimTokenProvider.requestTimToken()
        if (imToken == null) {
            return false
        }
        var handled = false
        IMLogUtils.i("TAG", "tim login $userId $imToken")
        return suspendCoroutine {
            V2TIMManager.getInstance().login(
                userId,
                imToken,
                object : V2TIMCallback {
                    override fun onSuccess() {
                        IMLogUtils.i("tim login onSuccess")
                        if (handled) {
                            return
                        }
                        handled = true
                        registerPush()

                        // fixed 这里在登陆成功后加入直播群
                        IMUtils.launchOnUnMain {
                            joinConversation(
                                RtmManager.getTimGlobalGroupId(IMCompatCore.currentUser?.isJP == true),
                                ConversationType.RTM,
                                true,
                            )
                        }
                        appCoroutineScope.launch {
                            SayHiLogicUtils.sayHi()
                        }
                        it.resume(true)
                    }

                    override fun onError(
                        code: Int,
                        desc: String,
                    ) {
                        IMLogUtils.i("tim login onError $code $desc")
                        if (handled) {
                            return
                        }
                        handled = true
                        when (code) {
                            BaseConstants.ERR_SVR_ACCOUNT_USERSIG_MISMATCH_ID, BaseConstants.ERR_USER_SIG_EXPIRED,
                            BaseConstants.ERR_SVR_ACCOUNT_USERSIG_EXPIRED, BaseConstants.ERR_LOGIN_KICKED_OFF_BY_OTHER,
                            -> {
                                it.resumeWithException(IMReLoginException())
                            }

                            else -> {
                                it.resume(false)
                            }
                        }
                    }
                },
            )

            loadConversation?.invoke()
        }
    }

    override fun logout() {
        messageCache.evictAll()
        V2TIMManager.getInstance().logout(null)
    }

    //region 扩展字段
    private val messageCache by lazy {
        LruCache<String, CacheMessage>(20)
    }

    data class CacheMessage(
        private val customInt: Int, // 发送中的过程中不能使用sdk直接修改会失败
        private val customData: String, // 发送中的过程中不能使用sdk直接修改会失败
    ) {
        var customIntChanged = false
            private set

        var localCustomInt = customInt
            set(value) {
                customIntChanged = true
                field = value
            }

        var customDataChanged = false
            private set

        var localCustomData = customData
            set(value) {
                customDataChanged = true
                field = value
            }
    }

    fun getLocalCustomInt(message: V2TIMMessage): Int {
        if (message.status != V2TIMMessage.V2TIM_MSG_STATUS_SENDING) {
            return message.localCustomInt
        }
        return messageCache[message.msgID]?.takeIf { it.customIntChanged }?.localCustomInt ?: message.localCustomInt
    }

    fun setLocalCustomInt(
        message: V2TIMMessage,
        localCustomInt: Int,
    ) {
        if (message.status != V2TIMMessage.V2TIM_MSG_STATUS_SENDING) {
            message.localCustomInt = localCustomInt
            return
        }
        messageCache[message.msgID]?.localCustomInt = localCustomInt
    }

    fun getLocalCustomData(message: V2TIMMessage): String? {
        if (message.status != V2TIMMessage.V2TIM_MSG_STATUS_SENDING) {
            return message.localCustomData
        }
        return messageCache[message.msgID]?.takeIf { it.customDataChanged }?.localCustomData ?: message.localCustomData
    }

    fun setLocalCustomData(
        message: V2TIMMessage,
        localCustomData: String,
    ) {
        if (message.status != V2TIMMessage.V2TIM_MSG_STATUS_SENDING) {
            message.localCustomData = localCustomData
            return
        }
        messageCache[message.msgID]?.localCustomData = localCustomData
    }

    //endregion

    override fun attach(dispatcher: EventDispatcher) {
        TIMEngine.dispatcher = dispatcher
    }

    //region 上层操作

    override fun createMessage(
        params: SendParams,
        bundle: MessageBundle,
    ): UCMessage {
        val message =
            when (bundle) {
                is MessageBundle.Text -> V2TIMManager.getMessageManager().createTextMessage(bundle.text)
                is MessageBundle.Voice ->
                    V2TIMManager
                        .getMessageManager()
                        .createSoundMessage(bundle.localPath, bundle.duration)

                is MessageBundle.Image -> {
                    if (bundle.localPath.isNullOrEmpty() || Build.VERSION.SDK_INT == Build.VERSION_CODES.Q) {
                        val path = IMUtils.copyFileToCache(IMCompatCore.applicationContext, bundle.uri)
                        if (path.isNullOrEmpty()) {
                            throw IllegalArgumentException("createMessage: image file is null")
                        } else {
                            V2TIMManager.getMessageManager().createImageMessage(path)
                        }
                    } else {
                        V2TIMManager.getMessageManager().createImageMessage(bundle.localPath)
                    }
                }

                is MessageBundle.Custom ->
                    V2TIMManager
                        .getMessageManager()
                        .createCustomMessage(bundle.getCustomData().toByteArray(Charsets.UTF_8))
            }
        return TIMMessage(message, bundle)
    }

    override fun recallMessage(ucMessage: UCMessage) {
        if (ucMessage is TIMMessage) {
            // 会自动回调onRecvMessageRevoked和onConversationChanged不需要自己处理
            V2TIMManager.getMessageManager().revokeMessage(convertToMessage(ucMessage), null)
        }
    }

    override fun deleteMessage(ucMessage: UCMessage) {
        if (ucMessage is TIMMessage) {
            val message = convertToMessage(ucMessage)
            V2TIMManager.getMessageManager().deleteMessages(
                listOf(message),
                object : V2TIMCallback {
                    override fun onSuccess() {
                        // 删除云端消息成功
                        if (message != null) {
                            IMUtils.launchOnUnMain {
                                val deleteMsg = parseMsgFromOrigin(message).toUCInstanceMessage()
                                dispatcher?.dispatchListener {
                                    if (it.match(deleteMsg)) {
                                        it.onMessageDeleted(deleteMsg)
                                    }
                                }
                            }
                        }
                    }

                    override fun onError(
                        code: Int,
                        desc: String,
                    ) = Unit
                },
            )
        }
    }

    override fun markMessageReadReceipts(message: UCInstanceMessage) {
        val msg = convertToMessage(message.base) ?: return
        val conversationId = if (msg.userID.isBlank()) msg.groupID else msg.userID
        cleanConversationUnreadCount(conversationId, msg.timestamp + 500)
        V2TIMManager.getMessageManager().sendMessageReadReceipts(listOf(msg), null)
    }

    override fun markMessageReadReceipts(list: List<UCInstanceMessage>) {
        list.mapNotNull { convertToMessage(it.base) }.takeIf { it.isNotEmpty() }?.also {
            val message = it.maxBy { it.timestamp }
            val conversationId = if (message.userID.isBlank()) message.groupID else message.userID
            cleanConversationUnreadCount(conversationId, message.timestamp + 500)
            V2TIMManager.getMessageManager().sendMessageReadReceipts(it, null)
        }
    }

    override fun deleteConversation(
        id: String,
        type: ConversationType,
    ) {
        // 不需要调用 cleanConversationUnreadCount(conversation)，sdk内部会处理
        V2TIMManager
            .getConversationManager()
            .deleteConversationList(
                // conversationIDList =
                listOf(id.id2ConvId(type)), // clearMessage =
                false, // callback =
                object : V2TIMValueCallback<List<V2TIMConversationOperationResult>> {
                    override fun onSuccess(t: List<V2TIMConversationOperationResult>?) {
                        IMLogUtils.w("deleteConversation success. id=${t?.joinToString()}")
                    }

                    override fun onError(
                        code: Int,
                        desc: String?,
                    ) {
                        IMLogUtils.w("deleteConversation error. code=$code, desc=$desc")
                    }
                },
            )
    }

    override fun deleteConversation(ucConversation: UCConversation) {
        // 不需要调用 cleanConversationUnreadCount(conversation)，sdk内部会处理
        V2TIMManager
            .getConversationManager()
            .deleteConversationList(
                // conversationIDList =
                listOf(ucConversation.stableId), // clearMessage =
                false, // callback =
                null,
            )
    }

    override fun setPinnedConversation(
        id: String,
        type: ConversationType,
        isPinned: Boolean,
    ) {
        // 会自动回调onConversationChanged不需要自己处理
        V2TIMManager
            .getConversationManager()
            .pinConversation(id.id2ConvId(type), isPinned, null)
    }

    override fun setPinnedConversation(
        ucConversation: UCConversation,
        isPinned: Boolean,
    ) {
        // 会自动回调onConversationChanged不需要自己处理
        V2TIMManager
            .getConversationManager()
            .pinConversation(
                // conversationID =
                ucConversation.id, // isPinned =
                isPinned, // callback =
                object : V2TIMCallback {
                    override fun onSuccess() {
                        IMLogUtils.w("set pinned success. id=${ucConversation.stableId}")
                    }

                    override fun onError(
                        code: Int,
                        desc: String?,
                    ) {
                        IMLogUtils.w("set pinned error. code=$code, desc=$desc")
                    }
                },
            )
    }

    /**
     * 清空会话未读数, 会导致该会话所有消息被标记为已读
     */
    private fun cleanConversationUnreadCount(
        conversationId: String,
        cleanTimestamp: Long = 0,
    ) {
        V2TIMManager
            .getConversationManager()
            .cleanConversationUnreadMessageCount(
                // conversationID =
                conversationId, // cleanTimestamp =
                cleanTimestamp, // cleanSequence =
                0, // callback =
                null,
            )
    }

    override fun cleanConversationUnreadCount(
        id: String,
        type: ConversationType,
    ) {
        cleanConversationUnreadCount(id.id2ConvId(type), 0L)
    }

    override fun cleanConversationUnreadCount(ucConversation: UCConversation) {
        cleanConversationUnreadCount(ucConversation.stableId, 0L)
    }

    override fun cleanAllConversationUnreadCount() {
        // 会自动回调onConversationChanged不需要自己处理
        V2TIMManager
            .getConversationManager()
            .cleanConversationUnreadMessageCount(
                // conversationID =
                "", // cleanTimestamp =
                0, // cleanSequence =
                0, // callback =
                null,
            )
    }

    override fun setConversationReceiveMessageOpt(
        id: String,
        type: ConversationType,
        notDisturbEnable: Boolean,
    ) {
        if (type == ConversationType.C2C) {
            V2TIMManager.getMessageManager().setC2CReceiveMessageOpt(
                // userIDList =
                listOf(id), // opt =
                if (notDisturbEnable) V2TIMMessage.V2TIM_RECEIVE_NOT_NOTIFY_MESSAGE else V2TIMMessage.V2TIM_RECEIVE_MESSAGE, // callback =
                null,
            )
        } else {
            V2TIMManager.getMessageManager().setGroupReceiveMessageOpt(
                // groupID =
                id, // opt =
                if (notDisturbEnable) V2TIMMessage.V2TIM_RECEIVE_NOT_NOTIFY_MESSAGE else V2TIMMessage.V2TIM_RECEIVE_MESSAGE, // callback =
                null,
            )
        }
    }

    override suspend fun joinConversation(
        id: String,
        type: ConversationType,
        clearHistory: Boolean,
    ): Boolean {
        if (clearHistory) {
            V2TIMManager.getMessageManager().clearGroupHistoryMessage(id, null)
        }
        // 加入群组
        return suspendCoroutine {
            V2TIMManager.getInstance().joinGroup(
                id,
                "",
                object : V2TIMCallback {
                    override fun onSuccess() {
                        it.resume(true)
                    }

                    override fun onError(
                        code: Int,
                        desc: String?,
                    ) {
                        if (code == BaseConstants.ERR_SVR_GROUP_ALLREADY_MEMBER) {
                            it.resume(true)
                        } else {
                            it.resume(false)
                        }
                    }
                },
            )
        }
    }

    override suspend fun quitConversation(
        id: String,
        type: ConversationType,
        clearHistory: Boolean,
    ): Boolean {
        if (clearHistory) {
            V2TIMManager.getMessageManager().clearGroupHistoryMessage(id, null)
        }
        // 退出群组
        return suspendCoroutine {
            V2TIMManager.getInstance().quitGroup(
                id,
                object : V2TIMCallback {
                    override fun onSuccess() {
                        it.resume(true)
                    }

                    override fun onError(
                        code: Int,
                        desc: String?,
                    ) {
                        it.resume(false)
                    }
                },
            )
        }
    }

    override suspend fun getConversations(
        nextSeq: Long,
        count: Int,
    ): IMConversationResult? =
        suspendCoroutine {
            V2TIMManager
                .getConversationManager()
                .getConversationList(
                    nextSeq,
                    count,
                    object : V2TIMValueCallback<V2TIMConversationResult> {
                        override fun onSuccess(v2TIMConversationResult: V2TIMConversationResult) {
                            val ret =
                                IMConversationResult(
                                    isFinished = v2TIMConversationResult.isFinished,
                                    nextSeq = v2TIMConversationResult.nextSeq,
                                    list =
                                        v2TIMConversationResult.conversationList
                                            .orEmpty()
                                            .toUCConversationListByTIM(),
                                )
                            // 获取会话列表成功
                            it.resume(ret)
                        }

                        override fun onError(
                            code: Int,
                            desc: String,
                        ) {
                            // 获取会话列表失败
                            it.resume(null)
                        }
                    },
                )
        }

    override suspend fun getIMConversation(
        id: String,
        type: ConversationType,
        excludeCache: Boolean,
    ): UCConversation? {
        val conversationId = id.id2ConvId(type)
        return suspendCoroutine {
            V2TIMManager
                .getConversationManager()
                .getConversation(
                    conversationId,
                    object : V2TIMValueCallback<V2TIMConversation> {
                        override fun onSuccess(conversation: V2TIMConversation) {
                            if (conversation.checkValid(type)) {
                                it.resume(parseConvFromOrigin(conversation))
                            } else {
                                it.resume(null)
                            }
                        }

                        override fun onError(
                            code: Int,
                            desc: String?,
                        ) {
                            it.resume(null)
                        }
                    },
                )
        }
    }

    override suspend fun getHistoryMessages(
        id: String,
        type: ConversationType,
        count: Int,
        lastMsg: UCMessage?,
        descendPullOrder: Boolean,
        onlyLocal: Boolean,
    ): List<UCInstanceMessage> {
        val getType =
            if (onlyLocal) {
                if (descendPullOrder) {
                    V2TIMMessageListGetOption.V2TIM_GET_LOCAL_OLDER_MSG
                } else {
                    V2TIMMessageListGetOption.V2TIM_GET_LOCAL_NEWER_MSG
                }
            } else {
                if (descendPullOrder) {
                    V2TIMMessageListGetOption.V2TIM_GET_CLOUD_OLDER_MSG
                } else {
                    V2TIMMessageListGetOption.V2TIM_GET_CLOUD_NEWER_MSG
                }
            }
        // 拉取历史消息
        // 首次拉取，lastMsg 设置为 null
        // 再次拉取时，lastMsg 可以使用返回的消息列表中的最后一条消息
        val lastMessage = convertToMessage(lastMsg)
        if (lastMsg != null && lastMessage == null) {
            return listOf()
        }
        val option =
            V2TIMMessageListGetOption().apply {
                setGetType(getType) // 当设置从云端拉取时，会将本地存储消息列表与云端存储消息列表合并后返回。如果无网络，则直接返回本地消息列表
                setLastMsg(lastMessage) // 设置从最新的消息开始拉取
                setCount(count) // 拉取 20 条消息
                if (type == ConversationType.C2C) {
                    userID = id // 拉取单聊消息
                } else {
                    groupID = id
                }
            }
        val messageList =
            getHistoryMessages(option)?.map { parseMsgFromOrigin(it).toUCInstanceMessage() } ?: listOf()

        return messageList
    }

    private suspend fun getHistoryMessages(option: V2TIMMessageListGetOption): List<V2TIMMessage>? {
        // 拉取历史消息
        // 首次拉取，lastMsg 设置为 null
        // 再次拉取时，lastMsg 可以使用返回的消息列表中的最后一条消息
        return suspendCoroutine {
            V2TIMManager
                .getMessageManager()
                .getHistoryMessageList(
                    option,
                    object : V2TIMValueCallback<List<V2TIMMessage>> {
                        override fun onSuccess(list: List<V2TIMMessage>) {
                            if (option.getType == V2TIMMessageListGetOption.V2TIM_GET_LOCAL_NEWER_MSG ||
                                option.getType == V2TIMMessageListGetOption.V2TIM_GET_LOCAL_NEWER_MSG
                            ) {
                                it.resume(list.reversed())
                            } else {
                                it.resume(list)
                            }
                        }

                        override fun onError(
                            code: Int,
                            desc: String,
                        ) {
                            it.resume(null)
                        }
                    },
                )
        }
    }

    override suspend fun getHistoryMessages(
        id: String,
        type: ConversationType,
        sequence: Long,
        before: Int,
        after: Int,
    ): List<UCInstanceMessage>? {
        val isC2C = type == ConversationType.C2C
        var option =
            V2TIMMessageListGetOption().apply {
                getType = V2TIMMessageListGetOption.V2TIM_GET_CLOUD_NEWER_MSG // 拉取比群 @ 消息更晚的消息
                lastMsgSeq = sequence + 1 // 从群 @ 消息的后一条消息开始拉取，不包括群 @ 消息
                count = after // 拉取 20 条消息
                if (isC2C) {
                    userID = id // 拉取单聊消息
                } else {
                    groupID = id
                }
            }

        val list1 =
            suspendCoroutine {
                V2TIMManager
                    .getMessageManager()
                    .getHistoryMessageList(
                        option,
                        object : V2TIMValueCallback<List<V2TIMMessage>> {
                            override fun onSuccess(list: List<V2TIMMessage>) {
                                it.resume(list.reversed())
                            }

                            override fun onError(
                                code: Int,
                                desc: String,
                            ) {
                                it.resume(null)
                            }
                        },
                    )
            }

        option =
            V2TIMMessageListGetOption().apply {
                getType = V2TIMMessageListGetOption.V2TIM_GET_CLOUD_OLDER_MSG // 拉取比群 @ 消息更早的消息
                lastMsgSeq = sequence // 设置从最新的消息开始拉取
                count = before // 拉取 20 条消息
                if (isC2C) {
                    userID = id // 拉取单聊消息
                } else {
                    groupID = id
                }
            }

        val list2 =
            suspendCoroutine {
                V2TIMManager
                    .getMessageManager()
                    .getHistoryMessageList(
                        option,
                        object : V2TIMValueCallback<List<V2TIMMessage>> {
                            override fun onSuccess(list: List<V2TIMMessage>) {
                                it.resume(list)
                            }

                            override fun onError(
                                code: Int,
                                desc: String,
                            ) {
                                it.resume(null)
                            }
                        },
                    )
            }

        if (list1 == null && list2 == null) {
            return null
        }

        return buildList {
            if (list1 != null) {
                list1
                    .map {
                        parseMsgFromOrigin(it).toUCInstanceMessage()
                    }.let { addAll(it) }
            }
            if (list2 != null) {
                list2
                    .map {
                        parseMsgFromOrigin(it).toUCInstanceMessage()
                    }.let { addAll(it) }
            }
        }
    }

    override suspend fun sendMessage(
        params: SendParams,
        mainMessage: UCMessage,
    ) {
        val message = mainMessage as? TIMMessage ?: return
        var rawMessage = message.rawMessage
        val onlyLocal = params.onlyLocal
        var msgId: String? = null
        val isResend = !onlyLocal && message.sendStatus == MsgSendStatus.Failure
        val callback =
            if (!onlyLocal) {
                // 发送消息期间V2TIMMessage始终是同一个对象，只是内部的message发生了改变
                object : V2TIMSendCallback<V2TIMMessage> {
                    override fun onSuccess(msg: V2TIMMessage) {
                        if (isResend) {
                            IMLogUtils.i("重发消息成功 => $rawMessage")
                        } else {
                            IMLogUtils.i("发送消息成功 => $rawMessage")
                        }
                        if (msgId == null) {
                            return
                        }
                        messageCache.remove(msgId!!)?.also {
                            if (it.customIntChanged && msg.localCustomInt != it.localCustomInt) {
                                msg.localCustomInt = it.localCustomInt
                            }
                            if (it.customDataChanged && msg.localCustomData != it.localCustomData) {
                                msg.localCustomData = it.localCustomData
                            }
                        }
                        IMUtils.launchOnUnMain {
                            val ucMessage = parseMsgFromOrigin(msg).toUCInstanceMessage()
                            dispatcher?.dispatchListener {
                                if (it.match(ucMessage)) {
                                    it.onSendMessageResult(ucMessage, true)
                                }
                            }
                        }
                    }

                    override fun onError(
                        code: Int,
                        desc: String,
                    ) {
                        if (isResend) {
                            IMLogUtils.i("重发消息失败code$code => desc:$desc => $rawMessage")
                        } else {
                            IMLogUtils.i("发送消息失败code$code => desc:$desc => $rawMessage")
                        }
                        if (msgId == null) {
                            return
                        }
                        messageCache.remove(msgId!!)?.also {
                            if (it.customIntChanged && rawMessage.localCustomInt != it.localCustomInt) {
                                rawMessage.localCustomInt = it.localCustomInt
                            }
                            if (it.customDataChanged && rawMessage.localCustomData != it.localCustomData) {
                                rawMessage.localCustomData = it.localCustomData
                            }
                        }

                        IMUtils.launchOnUnMain {
                            val timMessage = parseMsgFromOrigin(rawMessage)
                            if (code in 120001..130000 &&
                                desc.isNotEmpty() &&
                                desc.contains(TIMMessage.CLOUD_AND_LOCAL_CUSTOM_KEY_EXTENSIONS)
                            ) {
                                (
                                    AppJson.parseToJsonElement(
                                        desc,
                                    ) as? JsonObject
                                )?.parseValue<TimMsgExtensions?>(TIMMessage.CLOUD_AND_LOCAL_CUSTOM_KEY_EXTENSIONS)
                                    ?.also {
                                        timMessage.updateCloudCustomData(
                                            TIMMessage.CLOUD_AND_LOCAL_CUSTOM_KEY_EXTENSIONS,
                                            it,
                                        )
                                    }
                            }
                            val ucMessage = timMessage.toUCInstanceMessage()
                            dispatcher?.dispatchListener {
                                if (it.match(ucMessage)) {
                                    it.onSendMessageResult(ucMessage, false)
                                }
                            }
                        }
                    }

                    override fun onProgress(progress: Int) = Unit
                }
            } else {
                null
            }

        val type = params.type
        rawMessage.isExcludedFromLastMessage = params.isExcludedFromLastMessage
        rawMessage.isExcludedFromUnreadCount = params.isExcludedFromUnreadCount
        // 直播群消息不支持扩展
        rawMessage.isSupportMessageExtension = type != ConversationType.RTM
        rawMessage.isNeedReadReceipt = type == ConversationType.C2C

        if (!isResend) {
            val user = IMCompatCore.currentUser?.toIMUser()
            if (user == null) {
                IMLogUtils.w("当前的user = null, 不能转换成IMUser")
                return
            }
            message.setSendIMUser(user)

            val atUserList =
                params.atInfo?.run {
                    buildList {
                        if (withAtAll) {
                            add(ConversationAtInfo.AT_ALL_TAG)
                        }
                        atUsers?.also {
                            addAll(it)
                        }
                    }.takeIf { it.isNotEmpty() }
                }
            if (atUserList != null) {
                rawMessage = V2TIMManager.getMessageManager().createAtSignedGroupMessage(rawMessage, atUserList)
            }
        }

        if (onlyLocal) {
            val cb =
                object : V2TIMValueCallback<V2TIMMessage> {
                    override fun onSuccess(msg: V2TIMMessage) {
                        // 确定会话刷新回调会自动回调
                        IMUtils.launchOnUnMain {
                            val ucMessage = TIMMessage(msg).toUCInstanceMessage()
                            dispatcher?.dispatchListener {
                                if (it.match(ucMessage)) {
                                    it.onSendNewMessage(ucMessage, true)
                                }
                            }
                        }
                    }

                    override fun onError(
                        code: Int,
                        desc: String?,
                    ) = Unit
                }
            val userId = mUserId ?: ""
            if (type == ConversationType.C2C) {
                V2TIMManager.getMessageManager().insertC2CMessageToLocalStorage(rawMessage, params.receiver, userId, cb)
            } else {
                V2TIMManager
                    .getMessageManager()
                    .insertGroupMessageToLocalStorage(rawMessage, params.receiver, userId, cb)
            }
            return
        }

        val offlinePushInfo =
            message.toUCInstanceMessage().toPushInfo(params)?.let {
                V2TIMOfflinePushInfo().apply {
                    title = it.title
                    desc = it.desc
                    setAndroidFCMImage(it.avatar)
                    setIOSImage(it.avatar)
                    setAndroidFCMChannelID("ucoo-tim-fcm")
                }
            }

        msgId =
            V2TIMManager.getMessageManager().sendMessage(
                // message =
                rawMessage, // receiver =
                if (type == ConversationType.C2C) params.receiver else null,
                if (type != ConversationType.C2C) params.receiver else null,
                V2TIMMessage.V2TIM_PRIORITY_NORMAL,
                false,
                offlinePushInfo,
                callback,
            )
        messageCache.put(
            msgId,
            CacheMessage(rawMessage.localCustomInt, rawMessage.cloudCustomData),
        )

        if (!msgId.isNullOrEmpty()) {
            if (isResend) {
                IMUtils.launchOnUnMain {
                    val ucMessage = parseMsgFromOrigin(rawMessage).toUCInstanceMessage()
                    IMLogUtils.i("重发消息通知:$ucMessage")
                    dispatcher?.dispatchListener {
                        if (it.match(ucMessage)) {
                            it.onResendMessage(ucMessage)
                        }
                    }
                }
            } else {
                // 确定会话刷新回调会自动回调
                IMUtils.launchOnUnMain {
                    val ucMessage = TIMMessage(rawMessage).toUCInstanceMessage()
                    dispatcher?.dispatchListener {
                        if (it.match(ucMessage)) {
                            it.onSendNewMessage(ucMessage, false)
                        }
                    }
                }
            }
            // 会自动回调onConversationChanged不需要自己处理
        }
    }

    override fun setMsgExtraForSendMessage(
        message: UCMessage,
        extra: JsonObject,
    ) {
        val msg = message as? TIMMessage ?: return
        msg.updateCloudCustomData(extra)
    }

    private var hasFcmToken = false

    private fun registerPush() {
        TIMPushManager.getInstance().registerPush(
            IMCompatCore.applicationContext,
            EnvironmentManager.current.tencentAppId,
            "",
            object : TIMPushCallback<Any?>() {
                override fun onSuccess(data: Any?) {
                    (data as? String?)?.also { token ->
                        hasFcmToken = true
                    }
                }
            },
        )
    }
    //endregion

    //region 业务操作: 发送前检查/收到消息后翻译
    override suspend fun checkMessageCanSend(
        params: SendParams,
        bundle: MessageBundle?,
        message: UCMessage,
    ): Boolean {
        // 不是本地插入消息且不是重发消息，在发送时需要校验是否可以发送
        return if (!params.onlyLocal && message.sendStatus != MsgSendStatus.Failure) {
            if (params.type == ConversationType.C2C) { // 如果是私聊就需要检查是否能发送
                val ret = checkCanSendMessageByC2C(params.receiver)
                ret?.takeIf { it.canChat }?.msgExtra?.takeIf { it.isNotEmpty() }?.apply {
                    setMsgExtraForSendMessage(message, this)
                }
                ret?.canChat == true
            } else if (params.type == ConversationType.CHATROOM && bundle is MessageBundle.Text) {
                executeApiCallExpectingData {
                    IMApi.instance.checkPublicScreenMessage(mapOf("text" to bundle.text))
                }.getOrNull()?.parseValue<Boolean>("is_ok") ?: false
            } else {
                true
            }
        } else {
            true
        }
    }

    private suspend fun checkCanSendMessageByC2C(receiver: String): MsgSendCondition? =
        executeApiCallExpectingData(showToastIfError = false) {
            IMApi.instance.checkMsgCanSend(mapOf("target_user_id" to receiver))
        }.onHttpFailure {
            if (it.httpStatusCode == 500 || it.httpStatusCode == 502) {
                showToast(it.errorMessage)
            }
        }.fold({ ret ->
            if (!ret.canChat) {
                dispatcher?.synchronizedDispatchListener {
                    if (it.match(receiver)) {
                        it.onMessageCheckFail(ret, null)
                    }
                }
            }
            ret
        }) { e ->
            dispatcher?.synchronizedDispatchListener {
                if (it.match(receiver)) {
                    it.onMessageCheckFail(null, e)
                }
            }
            null
        }
    //endregion

    private suspend fun getGroupRecvOpt(id: String): Boolean =
        suspendCoroutine { cont ->
            V2TIMManager.getGroupManager().getGroupsInfo(
                listOf(id),
                object : V2TIMValueCallback<List<V2TIMGroupInfoResult>> {
                    override fun onSuccess(v2TIMGroupProfileResults: List<V2TIMGroupInfoResult>) {
                        v2TIMGroupProfileResults
                            .find {
                                it.groupInfo.groupID == id
                            }?.also {
                                cont.resume(it.groupInfo.recvOpt == V2TIMMessage.V2TIM_RECEIVE_MESSAGE)
                            } ?: run {
                            cont.resume(false)
                        }
                    }

                    override fun onError(
                        code: Int,
                        desc: String,
                    ) {
                        cont.resume(false)
                    }
                },
            )
        }
}

private fun SelfUserInfo.toIMUser(): SendIMUser =
    SendIMUser(
        id = id,
        name = this.name,
        avatar = avatar,
        gender = gender,
        isVip = isVip,
        publicCP = cpRelationInfo?.publicCp,
        avatarFrame = avatarFrame,
        medalList = medalList.orEmpty(),
        level = level,
        age = age,
        cpUrl = cpRelationInfo?.cpExtraInfo?.levelInfo?.wakooSmallImgUrl,
        bubble = chatBubble,
        countryFlag = "",
        expLevelInfo = ExpLevelInfo(charmLevel, wealthLevel),
        colorfulNicknameGradient = decorations.colorfulNicknameGradient,
    )
