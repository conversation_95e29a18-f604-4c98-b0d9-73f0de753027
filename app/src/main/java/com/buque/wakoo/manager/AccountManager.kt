package com.buque.wakoo.manager

import android.content.Context
import android.webkit.CookieManager
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ProcessLifecycleOwner
import com.buque.wakoo.app.Const
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.bean.AccountInfo
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.CurrencyInfo
import com.buque.wakoo.bean.user.SelfOtherInfo
import com.buque.wakoo.bean.user.SelfUserInfo
import com.buque.wakoo.bean.user.SocialInfo
import com.buque.wakoo.bean.user.UserDecorations
import com.buque.wakoo.core.webview.offline.OfflinePkgManager
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.network.api.service.CommonApiService
import com.buque.wakoo.network.executeApiCall
import com.buque.wakoo.repository.AccountPreferencesRepository
import com.buque.wakoo.repository.PreviewAccountPreferencesRepository
import com.buque.wakoo.utils.preload.AppPreLoader
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

object AccountManager {
    private val accountPreferencesRepository =
        try {
            AccountPreferencesRepository()
        } catch (_: Exception) {
            PreviewAccountPreferencesRepository()
        }

    // 公开的、只读的 StateFlow，供外部（如ViewModel）观察用户状态
    val accountStateFlow: StateFlow<AccountInfo?> = accountPreferencesRepository.accountInfoFlow

    val userStateFlow: StateFlow<SelfUserInfo?> =
        accountStateFlow
            .map { it?.userInfo }
            .stateIn(
                appCoroutineScope,
                SharingStarted.Eagerly,
                accountStateFlow.value?.userInfo,
            )

    private var isInitialized = false

    val isLoggedIn: Boolean
        get() = accountStateFlow.value != null

    val selfUser: SelfUserInfo?
        get() = userStateFlow.value

    /**
     * 初始化 UserManager。必须在 Application 的 onCreate 中调用一次。
     * @param context 应用程序上下文，用于初始化 UserPreferencesRepository
     */
    fun initialize(context: Context) {
        if (isInitialized) {
            return
        }
        isInitialized = true
        // 启动一个协程，持续监听来自 DataStore 的数据变化
        appCoroutineScope.launch {
            launch {
                delay(100)
                if (isLoggedIn) { // 如果保存有用户信息，冷启动自动登录，刷新自己的数据
                    UserManager.getRemoteSelfUserInfo()
                }
            }

            accountStateFlow
                .distinctUntilChangedBy { item ->
                    item?.id
                }.collectLatest { accountData ->
                    if (accountData != null) { // 登录成功
                        Firebase.crashlytics.setUserId(accountData.publishId)
                        // 预加载相关资源
                        syncCookieToken(accountData.apiAuthToken)
                        AppPreLoader.loadAfterLoggedIn()
                        IMCompatCore.login(accountData.id, accountData.imToken)
                    } else {
                        LiveRoomManager.exitCurrentRoom()
                        clearCookie()
                        IMCompatCore.logout()
                    }
                }
        }

        appStatusSync()
    }

    @OptIn(FlowPreview::class)
    private fun appStatusSync() {
        var coldStartCondition = true

        appCoroutineScope.launch {
            accountStateFlow
                .distinctUntilChangedBy { item ->
                    item?.id
                }.collectLatest { accountData ->
                    if (accountData != null) {
                        ProcessLifecycleOwner
                            .get()
                            .lifecycle.currentStateFlow
                            .map { targetState ->
                                targetState.isAtLeast(Lifecycle.State.STARTED)
                            }.distinctUntilChanged()
                            .debounce(250)
                            .collectLatest { atStarted ->
                                val isColdStart = coldStartCondition
                                appCoroutineScope.launch {
                                    executeApiCall {
                                        CommonApiService.instance.appStatusSync(
                                            mapOf("is_using" to atStarted.toString(), "is_cold_start" to isColdStart.toString()),
                                        )
                                    }
                                }
                                if (atStarted && coldStartCondition) {
                                    coldStartCondition = false
                                }
                            }
                    }
                }
        }
    }

    /**
     * 用户登录
     * @param accountInfo 登录成功后获取的用户信息
     */
    suspend fun login(accountInfo: AccountInfo) {
        checkInitialized()
        accountPreferencesRepository.saveData(accountInfo)
    }

    /**
     * 用户退出登录
     */
    suspend fun logout(msg: String? = null) {
        checkInitialized()
        if (accountPreferencesRepository.clearData()) {
            if (msg != null) {
                showToast(msg)
            }
        }
    }

    suspend fun updateSelfUserInfo(action: (user: SelfUserInfo) -> SelfUserInfo) {
        checkInitialized()
        withContext(NonCancellable) {
            accountPreferencesRepository.updateInfo {
                it.copy(userInfo = action(it.userInfo))
            }
        }
    }

    suspend fun setApiTokens(
        accessToken: String,
        refreshToken: String,
    ) {
        checkInitialized()
        syncCookieToken(accessToken)
        accountPreferencesRepository.updateInfo {
            it.copy(
                tokenInfo =
                    it.tokenInfo.copy(
                        apiAuthToken = accessToken,
                        apiRefreshToken = refreshToken,
                    ),
            )
        }
    }

    private fun syncCookieToken(authToken: String?) {
        val value =
            if (authToken.isNullOrBlank()) {
                "Access-Token="
            } else {
                "Access-Token=$authToken"
            }
        val urls =
            arrayOf(
                "https://fastapi.wakooclub.com",
                "https://api.test.wakooclub.com",
                "https://api.wakooclub.com",
                "https://apitest.wakooclub.com",
                "https://apitest.ucoofun.com",
                Const.LOCAL_WEB_URL,
                OfflinePkgManager.hostOfflinePages,
            )
        try {
            CookieManager.getInstance().apply {
                for (url in urls) {
                    setAcceptCookie(true)
                    setCookie(url, value)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun clearCookie() {
        CookieManager.getInstance().removeAllCookies(null)
    }

    /**
     * 获取当前的认证Token。
     * 可以在需要发起网络请求的地方（例如 Retrofit 的 Interceptor）调用。
     * @return 当前用户的 authToken，如果未登录则返回 null。
     */
    fun getAccessToken(): String? {
        checkInitialized()
        return accountStateFlow.value?.apiAuthToken
    }

    /**
     * 获取当前的刷新Token。
     * @return 当前用户的 refreshToken，如果未登录则返回 null。
     */
    fun getRefreshToken(): String? {
        checkInitialized()
        return accountStateFlow.value?.apiRefreshToken
    }

    /**
     * 检查 UserManager 是否已初始化。
     */
    private fun checkInitialized() {
        if (!isInitialized) {
            throw IllegalStateException("UserManager must be initialized in Application.onCreate()")
        }
    }

    suspend fun updateSelfBasicInfo(updateAction: (BasicUser) -> BasicUser) {
        updateSelfUserInfo {
            it.copy(user = updateAction(it.user))
        }
    }

    suspend fun updateSelfDecorations(updateAction: (UserDecorations) -> UserDecorations) {
        updateSelfUserInfo {
            it.copy(decorations = it.decorations.let(updateAction))
        }
    }

    suspend fun updateSelfSocialInfo(updateAction: (SocialInfo) -> SocialInfo) {
        updateSelfUserInfo {
            it.copy(socialInfo = it.socialInfo.let(updateAction))
        }
    }

    suspend fun updateSelfCurrencyInfo(updateAction: (CurrencyInfo) -> CurrencyInfo) {
        updateSelfUserInfo {
            it.copy(currencyInfo = it.currencyInfo.let(updateAction))
        }
    }

    suspend fun updateSelfOtherInfo(updateAction: (SelfOtherInfo) -> SelfOtherInfo) {
        updateSelfUserInfo {
            it.copy(otherInfo = it.otherInfo.let(updateAction))
        }
    }
}
