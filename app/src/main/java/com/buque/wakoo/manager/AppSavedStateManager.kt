package com.buque.wakoo.manager

import android.os.Bundle
import androidx.savedstate.SavedStateRegistry
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.Const
import com.buque.wakoo.im.utils.takeIsNotBlank

object AppSavedStateManager {
    fun bind(savedStateRegistry: SavedStateRegistry) {
        savedStateLiveRoom(savedStateRegistry)
    }

    private fun savedStateLiveRoom(savedStateRegistry: SavedStateRegistry) {
        savedStateRegistry.registerSavedStateProvider(
            Const.SavedState.LIVE_ROOM,
        ) {
            val bundle = Bundle()
            val current = LiveRoomManager.getCurrentSaveStateRoom()
            if (current != null) {
                bundle.putString(Const.SavedState.DATA, AppJson.encodeToString(current))
            }
            bundle
        }

        // 恢复数据
        val restoredBundle = savedStateRegistry.consumeRestoredStateForKey(Const.SavedState.LIVE_ROOM)
        restoredBundle?.let {
            if (it.isEmpty) {
                return
            }
            val saveStateRoom =
                it.getString(Const.SavedState.DATA)?.takeIsNotBlank()?.let { value ->
                    AppJson.decodeFromString<SaveStateRoom?>(value)
                }
            if (saveStateRoom != null) {
                LiveRoomManager.restoreSaveStateRoom(saveStateRoom)
            }
        }
    }
}
