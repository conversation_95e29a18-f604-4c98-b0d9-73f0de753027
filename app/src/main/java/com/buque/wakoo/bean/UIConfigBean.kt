package com.buque.wakoo.bean

import com.buque.wakoo.ui.widget.SwiperData
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

/**
 * WAKOO-banner
 * <br>
 * 201. 全局；202. 发现页；203. 我的页面；204. 首页；205. 消息列表页'
 * <br>
 * Wakoo-挂件
 * 301-全局;302-首页；303-发现页；304-消息页；305-我的页；306-任务页（日区）；307-语音房；308-私聊页;
 */
@Serializable
data class PendantConfig(
    val pendants: List<ActivityPendant> = emptyList(),
    val banners: List<BannerInfo> = emptyList(),
    @SerialName("main_page_entrances")
    val mainFeatures: MainFeature = MainFeature(),
    @SerialName("audio_room_entrances")
    val audioEntrances: MainFeature = MainFeature(),
    val widgets: List<Widget> = emptyList(),
) {
    @Serializable
    data class Widget(
        val name: String,
        val t: Int,
        @SerialName("visible_pos")
        val visiblePoi: List<Int>,
    )

    @Serializable
    data class BannerInfo(
        @SerialName("id")
        override val id: Int,
        @SerialName("jump_link")
        val jumpLink: String,
        @SerialName("pic_url")
        override val picUrl: String,
        val type: Int,
        val slug: String = "",
        val position: List<Int> = emptyList(),
    ) : SwiperData {
        fun support(position: Int): Boolean = this.position.contains(201) || this.position.contains(position)

        override val isH5Pic: Boolean
            get() = type == 2
    }

    @Serializable
    data class Container(
        @SerialName("height") val heightRatio: Int = 0,
        val t: Int = 0,
        val title: String = "",
        @SerialName("main_btn_txt")
        val buttonText: String = "",
    )

    @Serializable
    data class MainFeature(
        val mains: List<FeatureItem> = listOf(),
        val others: List<FeatureItem> = listOf(),
    ) {
        companion object {
            private val exploreItemList = mutableListOf<String>()
        }

        init {
            mains.forEach {
                it.spanCount = 2
            }
        }

        @Transient
        val all = mains + others

        fun explore() {
            all.filter { exploreItemList.contains(it.name) }.forEach {
                exploreItemList.add(it.name)
            }
        }
    }

    @Serializable
    data class FeatureItem(
        val container: Container = Container(),
        val icon: String = "",
        val id: Int = 0,
        @SerialName("jump_link")
        val jumpLink: String = "",
        @SerialName("min_version")
        val minVersion: String = "",
        val name: String = "",
        val order: Int = 0,
        val position: List<Int> = listOf(),
        val slug: String = "",
        val type: Int = 0,
    ) {
        var spanCount = 1
    }

    @Serializable
    data class ActivityPendant(
        @SerialName("id")
        override val id: Int,
        @SerialName("icon")
        val icon: String,
        @SerialName("jump_link")
        val jumpLink: String = "",
        @SerialName("name")
        val name: String,
        @SerialName("type")
        val type: Int,
        @SerialName("visible_pos")
        val visiblePoi: List<Int> = emptyList(),
        val container: Container = Container(),
    ) : SwiperData {
        override val picUrl: String
            get() = icon

        fun support(position: Int): Boolean = this.visiblePoi.contains(301) || this.visiblePoi.contains(position)

        override val isH5Pic: Boolean
            get() = type == 2
    }
}
