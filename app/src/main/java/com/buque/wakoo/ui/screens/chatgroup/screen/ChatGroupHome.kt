package com.buque.wakoo.ui.screens.chatgroup.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.ext.click
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im.utils.WatchMessageEventEffect
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.ChatGroupRoute
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.icons.More
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.chatgroup.chat.ChatGroupPage
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooSecondarySelected
import com.buque.wakoo.ui.theme.WakooSecondaryUnSelected
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.widget.AdaptiveScrollableTabRow
import com.buque.wakoo.ui.widget.NoIndicationInteractionSource
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.WakooTitleBar
import com.buque.wakoo.ui.widget.adaptiveTabIndicatorOffset
import com.buque.wakoo.ui.widget.gift.GiftViewModel
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.requireData
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.eventBus.AppEvent
import com.buque.wakoo.utils.eventBus.EventBusEffect
import com.buque.wakoo.viewmodel.chatgroup.ChatGroupSettingsViewModel
import com.buque.wakoo.viewmodel.chatgroup.ChatGroupViewModel
import kotlinx.coroutines.launch

@Composable
fun ChatGroupHome(viewModel: ChatGroupSettingsViewModel) {
    val nav = LocalAppNavController.current
    val chatGroupId = viewModel.groupId
    val st by viewModel.state
    val lm = LocalLoadingManager.current
    val scope = rememberCoroutineScope()
    val density = LocalDensity.current
    val dialogController = rememberDialogController(render = false)

    if (st is CState.Success) {
        val chatGroup = st.requireData
        WatchMessageEventEffect(
            object : IMCompatListener {
                override fun onRecvNewCustomMessage(
                    message: UCCustomMessage,
                    offline: Boolean,
                ) {
                    LogUtils.d("rec chatgroup event:\n${message.getSummaryString()}")
                }
            },
        )

        val imId = chatGroup.imId
        val tabs = remember(Unit) {
            listOf("群组大厅".localized, "语音频道".localized)
        }
        val chatViewModel =
            viewModel<ChatGroupViewModel>(initializer = {
                ChatGroupViewModel(imId, density)
            })
        val giftViewModel =
            viewModel<GiftViewModel>(initializer = {
                GiftViewModel(chatGroupId, ConversationType.GROUP, imId)
            })
        val pagerState = rememberPagerState(pageCount = { tabs.size })
        val selectedTabIndex = pagerState.currentPage
        val scope = rememberCoroutineScope()

        EventBusEffect<AppEvent.Action> {
            if (it.actionName == "show_group_chat") {
                scope.launch {
                    pagerState.animateScrollToPage(0)
                }
            }
        }
        Box(modifier = Modifier.fillMaxSize()) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color(0xFFF5F7F9)), horizontalAlignment = Alignment.CenterHorizontally
            ) {
                WakooTitleBar(chatGroup.name, modifier = Modifier.background(Color.White), actions = {
                    val self = LocalSelfUserProvider.current
                    if (self.isCN) {
                        Image(
                            painter = painterResource(R.drawable.ic_group_cup),
                            contentDescription = "cup",
                            modifier = Modifier
                                .size(24.dp)
                                .click(onClick = {
                                    nav.push(ChatGroupRoute.GroupContributeRank)
                                })
                        )
                        SizeWidth(4.dp)
                    }
                    IconButton(onClick = {
                        nav.push(ChatGroupRoute.ChatGroupDetail(groupId = chatGroupId))
                    }) {
                        Icon(WakooIcons.More, "more", tint = MaterialTheme.colorScheme.onPrimary)
                    }
                })
                AdaptiveScrollableTabRow(
                    selectedTabIndex = selectedTabIndex,
                    tabSpacing = 20.dp,
                    indicator = { tabPositions ->
                        if (selectedTabIndex < tabPositions.size) {
                            Box(
                                modifier =
                                    Modifier
                                        .adaptiveTabIndicatorOffset(tabPositions[selectedTabIndex])
                                        .requiredWidth(12.dp)
                                        .height(3.dp)
                                        .background(
                                            WakooSecondarySelected,
                                            CircleShape,
                                        ),
                            )
                        }
                    },
                ) {
                    tabs.forEachIndexed { index, tab ->
                        Tab(
                            selected = selectedTabIndex == index,
                            selectedContentColor = WakooSecondarySelected,
                            unselectedContentColor = WakooSecondaryUnSelected,
                            onClick = {
                                scope.launch {
                                    pagerState.animateScrollToPage(index)
                                }
                            },
                            interactionSource = remember { NoIndicationInteractionSource() },
                            content = {
                                Box(
                                    modifier =
                                        Modifier
                                            .widthIn(min = 80.dp)
                                            .padding(top = 12.dp, bottom = 6.dp),
                                    contentAlignment = Alignment.Center,
                                ) {
                                    Text(tab, color = if (selectedTabIndex == index) WakooText else WakooGrayText)
                                }
                            },
                        )
                    }
                }
                HorizontalPager(pagerState) { index ->
                    if (index == 0) {
                        Box(modifier = Modifier.fillMaxSize()) {
                            ChatGroupPage(chatGroup, chatViewModel, giftViewModel, dialogController, onChangeBuilt = { newBuilt ->
                                lm.show(scope) {
                                    viewModel.update(bulletin = newBuilt)
                                }
                            }, onRefresh = { viewModel.refreshState() }, onMore = {
                                nav.push(ChatGroupRoute.ChatGroupDetail(groupId = chatGroupId))
                            })
                        }
                    } else {
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(top = 12.dp)
                                .navigationBarsPadding()
                        ) {
                            ChatGroupRoomListScreen(viewModel.groupId)
                        }
                    }

                }
            }
        }
        dialogController.RenderDialogs(chatGroup)
    }
}
