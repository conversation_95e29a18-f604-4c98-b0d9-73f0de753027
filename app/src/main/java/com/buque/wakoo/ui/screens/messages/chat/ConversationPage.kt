package com.buque.wakoo.ui.screens.messages.chat

import android.os.SystemClock
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Badge
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil3.request.transformations
import com.buque.wakoo.R
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.user.ONLINE_STATUS_LOGOUT
import com.buque.wakoo.bean.user.ONLINE_STATUS_ONLINE
import com.buque.wakoo.bean.user.UserProfileInfo
import com.buque.wakoo.ext.click
import com.buque.wakoo.ext.formatCount
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.inter.IUCConversation
import com.buque.wakoo.im_business.UIMessageUtils
import com.buque.wakoo.im_business.conversation.AppConversationManger
import com.buque.wakoo.im_business.conversation.BecomeToMemberConversation
import com.buque.wakoo.im_business.conversation.C2CConversation
import com.buque.wakoo.im_business.conversation.ConvLabel
import com.buque.wakoo.im_business.conversation.TribeConversation
import com.buque.wakoo.im_business.conversation.VisitorHistoryConversation
import com.buque.wakoo.manager.LiveRoomManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.screens.messages.NtfAlertState
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.CommonBanner
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.LoopHorizontalPager
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.SpaceListItemScaffold
import com.buque.wakoo.ui.widget.SwipeBox
import com.buque.wakoo.ui.widget.VoiceWaves
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.BlurTransformation
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.rememberSwipeBoxControl
import com.buque.wakoo.ui.widget.richtext.EntryRichText
import com.buque.wakoo.ui.widget.state.StateComponent
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.onEach

@Composable
fun ConversationPage(
    showBanner: Boolean = false,
    onAction: (IUCConversation) -> Unit,
) {
    val conversationList by AppConversationManger.conversationsFlow.collectAsStateWithLifecycle()
    val listState = rememberLazyListState()

    LaunchedEffect(key1 = listState) {
        snapshotFlow {
            if (conversationList.isNotEmpty() && !listState.isScrollInProgress) {
                val firstVisibleItemIndex =
                    listState.layoutInfo.visibleItemsInfo
                        .firstOrNull()
                        ?.index ?: 0
                val lastVisibleItemIndex =
                    (
                        listState.layoutInfo.visibleItemsInfo
                            .lastOrNull()
                            ?.index ?: 0
                    ).plus(1)
                Triple(
                    first = true,
                    second = firstVisibleItemIndex.minus(2).coerceAtLeast(0),
                    third = lastVisibleItemIndex.plus(2).coerceIn(0, conversationList.lastIndex),
                )
            } else {
                Triple(false, 0, 0)
            }
        }.filter {
            it.first && it.second < it.third
        }.onEach {
            val currentTime = SystemClock.elapsedRealtime()
            val checkStatusList =
                conversationList
                    .subList(it.second, (it.third + 1).coerceAtMost(conversationList.size))
                    .filter { item ->
                        item is C2CConversation &&
                            currentTime.minus(
                                AppConversationManger.getLastUpdateInfoTimestamp(item.id),
                            ) > 120_000
                    }.joinToString { item ->
                        AppConversationManger.setLastUpdateInfoTimestamp((item as C2CConversation).id, currentTime)
                        item.id
                    }
            AppConversationManger.updateUsersOnlineStatus(checkStatusList)
        }.collect()
    }

    LaunchedEffect(Unit) {
        AppConversationManger.refreshVisitorStatus()
    }

    Column(
        modifier =
            Modifier
                .fillMaxSize()
                .background(Color.White),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        NtfAlertState()
        if (showBanner) {
            CommonBanner(
                205,
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
            )
        }
        if (conversationList.isNotEmpty()) {
            LazyColumn(
                modifier =
                    Modifier
                        .weight(1f)
                        .background(Color.White),
            ) {
                items(conversationList) {
                    ConversationItem(it, onAction)
                }
            }
        } else {
            StateComponent.Empty(
                text = "暂无消息".localized,
                emptyId = R.drawable.ic_empty_for_notification,
                modifier = Modifier.weight(1f),
            )
        }
    }
}

@Composable
private fun ConversationItem(
    item: IUCConversation,
    onClick: (IUCConversation) -> Unit,
) {
    when (item) {
        is C2CConversation -> {
            C2CConversationWidget(item) {
                onClick(item)
            }
        }

        is TribeConversation -> {
            GroupConversation(item) {
                onClick(item)
            }
        }

        BecomeToMemberConversation -> {
        }

        VisitorHistoryConversation -> {
            ConversationVisitorItem {
                onClick(item)
            }
        }
    }
}

/**
 * 所有的数据都不需要在这里进行处理
 * 全部都由[AppConversationManger]组装完毕
 *
 * 要添加标签什么的, 要到[AppConversationManger.getLabels]去添加
 */
@Composable
private fun C2CConversationWidget(
    item: C2CConversation,
    onClick: () -> Unit,
) {
    val ctx = LocalContext.current
    val swipeController = rememberSwipeBoxControl()

    SwipeBox(
        actionWidth = 70.dp,
        id = "conversation_${item.stableId}",
        control = swipeController,
        onContentClick = onClick,
        endAction =
            listOf(
                // 置顶/取消置顶按钮
                {
                    Box(
                        modifier =
                            Modifier
                                .fillMaxSize()
                                .background(color = if (item.isPinned) Color(0xFFFF9500) else Color(0xFF007AFF))
                                .clickable {
                                    IMCompatCore.setPinnedConversation(item.stableId, ConversationType.C2C, !item.isPinned)
                                    swipeController.center()
                                },
                    ) {
                        Text(
                            text = if (item.isPinned) "取消置顶".localized else "置顶".localized,
                            modifier = Modifier.align(Alignment.Center),
                            style =
                                TextStyle.Default.copy(
                                    color = Color.White,
                                    fontSize = 12.sp,
                                ),
                        )
                    }
                },
                // 删除按钮
                {
                    Box(
                        modifier =
                            Modifier
                                .fillMaxSize()
                                .background(color = Color(0xFFFF3B30))
                                .clickable {
                                    IMCompatCore.deleteConversation(item.stableId, ConversationType.C2C)
                                    swipeController.center()
                                },
                    ) {
                        Text(
                            text = "删除".localized,
                            modifier = Modifier.align(Alignment.Center),
                            style =
                                TextStyle.Default.copy(
                                    color = Color.White,
                                    fontSize = 12.sp,
                                ),
                        )
                    }
                },
            ),
    ) {
        Row(
            Modifier
                .fillMaxWidth()
                .background(color = if (item.isPinned) Color(0xFFF7F7F7) else Color.Transparent)
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // 头像
            Box {
                val socialInfo = (item.user as? UserProfileInfo)?.socialInfo
                val roomId = socialInfo?.roomId ?: 0
                val onlineStatus = socialInfo?.onlineStatus ?: ONLINE_STATUS_LOGOUT
                AvatarNetworkImage(
                    user = item.user,
                    border =
                        BorderStroke(
                            2.dp,
                            if (roomId > 0) {
                                Color(0xffa3ff2c)
                            } else {
                                Color.Transparent
                            },
                        ),
                    onClick = {
                        if (onlineStatus == ONLINE_STATUS_ONLINE && roomId > 0) {
                            LiveRoomManager.joinRoom(roomId.toString())
                        } else {
                            it.push(Route.UserProfile(item.user.toBasic()))
                        }
                    },
                )

                if (onlineStatus == ONLINE_STATUS_ONLINE) {
                    if (roomId > 0) {
                        Row(
                            modifier =
                                Modifier
                                    .align(Alignment.BottomCenter)
                                    .background(
                                        brush = Brush.verticalGradient(listOf(Color(0xff68F81A), Color(0xff00ED77))),
                                        shape = CircleShape,
                                    ).padding(horizontal = 4.dp),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            VoiceWaves(contentColor = Color.White)
                            Text(
                                "语聊中".localized,
                                fontSize = 10.sp,
                                lineHeight = 10.sp,
                                color = Color.White,
                            )
                        }
                    } else {
                        Spacer(
                            Modifier
                                .align(Alignment.BottomEnd)
                                .padding(2.dp)
                                .size(10.dp)
                                .border(width = 1.dp, color = Color(0xFFFFFFFF), CircleShape)
                                .background(color = Color(0xFF13EC55), CircleShape),
                        )
                    }
                }
            }
            Spacer(Modifier.width(6.dp))
            // 内容
            Column(modifier = Modifier.weight(1f)) {
                // 标题/标签/时间
                Row(modifier = Modifier.fillMaxWidth()) {
                    Row(modifier = Modifier.weight(1f), verticalAlignment = Alignment.CenterVertically) {
                        Text(
                            buildString {
                                append(item.getDisplayName(ctx))
                            },
                            style = MaterialTheme.typography.bodyLarge,
                            color = Color(0xFF111111),
                            modifier = Modifier.weight(1f, false),
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                        )
                        SizeWidth(2.dp)
                        item.labels?.forEach { label ->
                            if (label is ConvLabel.ComposeUILabel) {
                                label.LabelContent()
                                SizeWidth(2.dp)
                            }
                        }
                    }
                    Text(
                        UIMessageUtils.getMessageTimeFormatText(item.timestamp),
                        color = Color(0xFF999999),
                        style = MaterialTheme.typography.labelLarge,
                    )
                }
                Spacer(Modifier.height(6.dp))
                // 内容/未读数
                Row {
                    Row(modifier = Modifier.weight(1f)) {
                        val hintExtra = item.hintExtra
                        if (hintExtra != null && hintExtra.hintVisible) {
                            val richList = hintExtra.hintRichList
                            if (richList != null) {
                                EntryRichText(
                                    rich = richList,
                                    color = Color(0xFFFF5E8B),
                                    fontSize = 14.sp,
                                    maxLines = 1,
                                    overflow = TextOverflow.Ellipsis,
                                )
                            } else {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.spacedBy(2.dp),
                                ) {
                                    if (!hintExtra.hintIcon.isNullOrEmpty()) {
                                        NetworkImage(
                                            hintExtra.hintIcon,
                                            modifier = Modifier.size(16.dp),
                                        )
                                    }

                                    Text(
                                        text = hintExtra.hintText.orEmpty(),
                                        color = if (hintExtra.isSelf) Color(0xff999999) else Color(0xffFE669E),
                                        fontSize = 14.sp,
                                        maxLines = 1,
                                        overflow = TextOverflow.Ellipsis,
                                    )
                                }
                            }
                        } else {
                            Text(
                                buildString {
                                    append(item.getDisplaySummary(ctx))
                                },
                                fontSize = 14.sp,
                                letterSpacing = 0.1.sp,
                                fontWeight = FontWeight.Medium,
                                color = Color(0xFF999999),
                                overflow = TextOverflow.Ellipsis,
                                maxLines = 1,
                            )
                        }
                    }

                    if (item.unreadCount > 0) {
                        Badge(containerColor = Color(0xffF53F3F)) {
                            Text(
                                buildString {
                                    append(if (item.unreadCount > 99) "99+" else item.unreadCount)
                                },
                                style =
                                    TextStyle(
                                        fontSize = 11.sp,
                                        lineHeight = 16.sp,
                                        fontWeight = FontWeight.Medium,
                                        color = Color.White,
                                    ),
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun GroupConversation(
    conv: TribeConversation,
    onClick: () -> Unit,
) {
    val ctx = LocalContext.current
    if (conv is TribeConversation.Instance) {
        Row(
            Modifier
                .fillMaxWidth()
                .padding(16.dp)
                .click(noEffect = true, onClick = onClick),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // 头像
            NetworkImage(
                conv.group.avatarUrl,
                modifier =
                    Modifier
                        .size(48.dp)
                        .clip(CircleShape),
            )
            Spacer(Modifier.width(6.dp))
            // 内容
            Column(modifier = Modifier.weight(1f)) {
                // 标题/标签/时间
                Row(modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                    Row(modifier = Modifier.weight(1f), verticalAlignment = Alignment.CenterVertically) {
                        Text(
                            buildString {
                                append(conv.getDisplayName(ctx))
                            },
                            modifier = Modifier.weight(1f, false),
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            style = MaterialTheme.typography.bodyLarge,
                            color = Color(0xFF111111),
                        )
                        SizeWidth(4.dp)
                        ConvLabel.GroupLabel.LabelContent()
                    }
                    Text(
                        UIMessageUtils.getMessageTimeFormatText(conv.imConversation.timestamp),
                        color = Color(0xFF999999),
                        style = MaterialTheme.typography.labelLarge,
                    )
                }
                Spacer(Modifier.height(6.dp))
                // 内容/未读数
                Row {
                    Text(
                        buildString {
                            append(conv.getDisplaySummary(ctx))
                        },
                        modifier = Modifier.weight(1f),
                        style = MaterialTheme.typography.labelLarge,
                        color = Color(0xFF999999),
                        overflow = TextOverflow.Ellipsis,
                        maxLines = 1,
                    )

                    if (conv.imConversation.unreadCount > 0) {
                        Badge(containerColor = Color(0xffF53F3F)) {
                            Text(
                                buildString {
                                    append(if (conv.imConversation.unreadCount > 99) "99+" else conv.imConversation.unreadCount)
                                },
                                style =
                                    TextStyle(
                                        fontSize = 11.sp,
                                        lineHeight = 16.sp,
                                        fontWeight = FontWeight.Medium,
                                        color = Color.White,
                                    ),
                            )
                        }
                    }
                }
            }
        }
    } else if (conv is TribeConversation.Empty) {
        Row(
            Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // 头像
            Image(
                painter = painterResource(R.drawable.ic_group_avatar_empty),
                modifier =
                    Modifier
                        .size(48.dp)
                        .clip(CircleShape),
                contentDescription = "",
            )
            Spacer(Modifier.width(6.dp))
            // 内容
            Column(modifier = Modifier.weight(1f)) {
                // 标题/标签/时间
                Row(modifier = Modifier.fillMaxWidth()) {
                    Row(modifier = Modifier.weight(1f)) {
                        Text(
                            buildString {
                                append(conv.getDisplayName(ctx))
                            },
                            style = MaterialTheme.typography.bodyLarge,
                            color = Color(0xFF111111),
                        )
                    }
                }
                Spacer(Modifier.height(6.dp))
                // 内容/未读数
                Row {
                    Text(
                        buildString {
                            append(conv.getDisplaySummary(ctx))
                        },
                        modifier = Modifier.weight(1f),
                        style = MaterialTheme.typography.labelLarge,
                        color = Color(0xFF999999),
                        overflow = TextOverflow.Ellipsis,
                        maxLines = 1,
                    )
                }
            }

            GradientButton(
                "加入群组".localized,
                onClick = onClick,
                height = 32.dp,
                fontSize = 12.sp,
                paddingValues = PaddingValues(horizontal = 12.dp),
            )
        }
    }
}

/**
 * 访问记录item
 */
@Composable
fun ConversationVisitorItem(
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    val badgeNumber by AppConversationManger.visitorCount.collectAsStateWithLifecycle()
    val list by AppConversationManger.visitorLists.collectAsStateWithLifecycle()
    SpaceListItemScaffold(
        modifier =
            modifier
                .click(onClick = onClick)
                .padding(16.dp, 12.dp),
        space = 6.dp,
        startContent = {
            if (list.isNotEmpty()) {
                Box {
                    Box(modifier = Modifier.size(48.dp)) {
                        val pageState = rememberPagerState { list.size.coerceAtLeast(1) }
                        LoopHorizontalPager(pageState, interval = 5000, modifier = Modifier.fillMaxSize()) {
                            AvatarNetworkImage(list[it], enabled = false, applyBuilder = {
                                if (SelfUser?.isVip == false) {
                                    transformations(BlurTransformation())
                                }
                            })
                        }
                    }
                    Badge(
                        modifier =
                            Modifier
                                .align(Alignment.TopEnd)
                                .offset(0.dp, (-2).dp)
                                .alpha(if (badgeNumber > 0) 1f else 0f)
                                .border(0.25.dp, Color(0xFFFFFFFF), CircleShape),
                        containerColor = Color(0xFFF76560),
                        contentColor = Color.White,
                    ) {
                        Text(text = badgeNumber.formatCount(99))
                    }
                }
            }
        },
        centerContent = {
            Column {
                Text(
                    text = "访客记录".localized,
                    style = MaterialTheme.typography.bodyLarge,
                    color = Color(0xFF111111),
                )
                Spacer(modifier = Modifier.height(10.dp))
                Text(
                    text =
                        buildAnnotatedString {
                            if (badgeNumber > 0) {
                                append("最近有%s人查看了你的主页".localizedFormat(badgeNumber))
                            } else {
                                append("我对你非常感兴趣".localized)
                            }
                        },
                    style = TextStyle(Color(0xFF86909C), 14.sp),
                )
            }
        },
        endContent = {
        },
    )
}
