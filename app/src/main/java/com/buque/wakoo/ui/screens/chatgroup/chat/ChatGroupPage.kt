@file:OptIn(ExperimentalMaterial3Api::class)

package com.buque.wakoo.ui.screens.chatgroup.chat

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material3.Badge
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.net.toUri
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.app.OnDataCallback
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.chatgroup.ChatGroupBean
import com.buque.wakoo.bean.chatgroup.ChatGroupMember
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.ext.click
import com.buque.wakoo.ext.hideKeyboardOnClickOutside
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.im.MessageBundle
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.bean.SendParams
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im_business.interf.IC2CAction
import com.buque.wakoo.im_business.message.MessageThemeBubble
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.im_business.message.types.UCImageMessage
import com.buque.wakoo.im_business.message.ui.entry.C2CContent
import com.buque.wakoo.im_business.message.ui.entry.ChatGroupMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.Content
import com.buque.wakoo.im_business.message.ui.entry.TimeMsgEntry
import com.buque.wakoo.im_business.panel.voice.AudioCenterStatusWidget
import com.buque.wakoo.im_business.wigets.AudioPanel
import com.buque.wakoo.im_business.wigets.C2CBottomBar
import com.buque.wakoo.im_business.wigets.C2CBottomPanel
import com.buque.wakoo.im_business.wigets.ChatScaffold
import com.buque.wakoo.im_business.wigets.EmojiPanel
import com.buque.wakoo.im_business.wigets.IMPanel
import com.buque.wakoo.im_business.wigets.KeyboardPanelState
import com.buque.wakoo.im_business.wigets.rememberPanelState
import com.buque.wakoo.im_business.wigets.withAutoHidePanel
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.navigation.ChatGroupRoute
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.DialogController
import com.buque.wakoo.navigation.dialog.easyPost
import com.buque.wakoo.navigation.dialog.easyPostBottomPanel
import com.buque.wakoo.navigation.rememberLauncherForResult
import com.buque.wakoo.ui.dialog.AnyPopDialogProperties
import com.buque.wakoo.ui.icons.Admin
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.chatgroup.panel.ChatGroupUserInfoPanel
import com.buque.wakoo.ui.screens.chatgroup.panel.GroupAnnouncement
import com.buque.wakoo.ui.screens.chatgroup.tasks.ui.GroupTaskDialogContent
import com.buque.wakoo.ui.screens.chatgroup.tasks.ui.GroupTaskPendant
import com.buque.wakoo.ui.screens.chatgroup.tasks.ui.TribeBoxViewModel
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.ui.widget.drag.FloatingLayoutManager
import com.buque.wakoo.ui.widget.drag.rememberDraggableFloatingState
import com.buque.wakoo.ui.widget.gift.GiftViewModel
import com.buque.wakoo.ui.widget.gift.GroupGiftPanel
import com.buque.wakoo.ui.widget.media.previewer.MediaViewerAlbum
import com.buque.wakoo.ui.widget.media.previewer.MediaViewerKey
import com.buque.wakoo.ui.widget.media.previewer.TransitionOverlay
import com.buque.wakoo.ui.widget.media.previewer.rememberPreviewState
import com.buque.wakoo.ui.widget.media.selector.MediaSelectorResult
import com.buque.wakoo.ui.widget.overScrollVertical
import com.buque.wakoo.ui.widget.rememberOverscrollFlingBehavior
import com.buque.wakoo.ui.widget.richtext.EntryRichText
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.eventBus.AppEvent
import com.buque.wakoo.utils.eventBus.EventBusEffect
import com.buque.wakoo.viewmodel.chatgroup.ChatGroupViewModel

private val colorSurface = Color(0xFFC8FFC9)
private val colorOnSurface = Color(0xFF1A7D1D)
private val bubbleShape = RoundedCornerShape(topEnd = 12.dp, bottomStart = 12.dp, bottomEnd = 12.dp)

@Composable
fun ChatGroupPage(
    chatGroupBean: ChatGroupBean,
    chatViewModel: ChatGroupViewModel,
    giftViewModel: GiftViewModel,
    dialogController: DialogController,
    onChangeBuilt: OnDataCallback<String>,
    modifier: Modifier = Modifier,
    onRefresh: OnAction = {},
    onMore: OnAction = {},
) {
    val targetImId = chatGroupBean.imId
    val sendParams =
        remember {
            SendParams(targetImId, ConversationType.GROUP)
        }
    val listState = rememberLazyListState()

    val roleState =
        remember(chatGroupBean) {
            val ms = chatGroupBean.firstPageMembers
            val ownerUid = ms.firstOrNull { it.role == ChatGroupMember.ROLE_OWNER }?.user?.id ?: ""
            val adminUidList = ms.filter { it.role == ChatGroupMember.ROLE_ADMIN }.map { it.user.id }.toList()
            mutableStateOf(ChatGroupRoles(ownerUid, adminUidList))
        }

    val paginateState = chatViewModel.bindListState(listState)
    val rootNavController = LocalAppNavController.root
    val launcher =
        rootNavController.rememberLauncherForResult<Route.MediaSelector, MediaSelectorResult> { result ->
            if (result.list.isNotEmpty()) {
                IMCompatCore.sendMessages(
                    sendParams,
                    result.list.map {
                        MessageBundle.Image.create(it.uriString.toUri(), "", it.width, it.height)
                    },
                )
            }
        }

    val panels =
        remember {
            arrayOf(
                IMPanel(AudioPanel, forceClearFocus = true), // 录音面板
                IMPanel(EmojiPanel, forceRequestFocus = true), // 表情面板
            )
        }
    val panelState = rememberPanelState(listState, panels, 12)
    val textFieldValue =
        rememberSaveable(stateSaver = TextFieldValue.Saver) {
            mutableStateOf(TextFieldValue())
        }
    val isLoading by remember {
        derivedStateOf {
            paginateState.nextLoadState.isLoading
        }
    }

    val density = LocalDensity.current

    fun showGiftPanel(targetUserList: List<User> = emptyList()) {
        dialogController.easyPost(
            dialogProperties = AnyPopDialogProperties(useCustomAnimation = true),
            content = {
                val giftModel = giftViewModel.giftListModelState
                LaunchedEffect(giftViewModel) {
                    giftViewModel.fetchGiftData()
                }
                GroupGiftPanel(null, giftModel, targetUserList, onAddClick = {
                    // todo 选择群组用户送礼
                }) { gift, params, users ->
                    giftViewModel.sendGiftToGroup(users.map { it.id }, gift, params)
                }
            },
        )
    }

    val boxVM = viewModel<TribeBoxViewModel>()

    EventBusEffect<AppEvent.Action> {
        when (it.actionName) {
            "show_gift_panel" -> {
                showGiftPanel()
            }

            "show_treasure_task" -> {
                dialogController.easyPostBottomPanel(dialogProperties = AnyPopDialogProperties(useSystemDialog = false)) {
                    GroupTaskDialogContent(boxVM, tabIndex = 0, onRefresh = onRefresh, onDismiss = {
                        dismiss()
                    })
                }
            }

            "show_active_task" -> {
                dialogController.easyPostBottomPanel(dialogProperties = AnyPopDialogProperties(useSystemDialog = false)) {
                    GroupTaskDialogContent(boxVM, tabIndex = 1, onRefresh = onRefresh, onDismiss = {
                        dismiss()
                    })
                }
            }

            else -> {}
        }
    }

    // 查看用户面板
    fun onCheckUser(user: User) {
        dialogController.easyPostBottomPanel {
            ChatGroupUserInfoPanel(user, onAtClick = {
                chatViewModel.addAtUser(user)
                val value = textFieldValue.value
                textFieldValue.value = value.copy(value.text + " @${user.name}")
            }, onSendGift = {
                showGiftPanel(listOf(user))
            }, onChat = {
                rootNavController.push(Route.Chat(user.toBasic()))
            })
        }
    }

    // 群组编辑dialog
    fun showGroupNoticeEditDialog() {
        dialogController.easyPost {
            GroupAnnouncement(
                chatGroupBean.bulletin,
                modifier = Modifier.width(270.dp),
                onChangeBuilt,
            )
        }
    }

    val previewState = rememberPreviewState()

    // 转场覆盖层，负责在转场期间显示动画元素和全屏查看器。
    TransitionOverlay(state = previewState)

    val onAction: IC2CAction =
        object : IC2CAction {
            override fun onSendMessage(message: MessageBundle) {
                chatViewModel.sendMessage(message)
            }

            override fun onSendMultipleMessage(messages: List<MessageBundle>) {
                if (messages.isNotEmpty()) {
                    chatViewModel.sendMessages(messages)
                } else {
                    showToast("消息发送失败".localized)
                }
            }

            override fun onResendMessage(message: UCInstanceMessage) {
                chatViewModel.sendMessage(message.base)
            }

            override fun onGoMediaSelector() {
                launcher.launch(Route.MediaSelector())
            }

            override fun onShowGiftPanel() {
                showGiftPanel()
            }

            override fun onPreview(message: UCInstanceMessage) {
                if (message !is UCImageMessage) return
                val (startIndex, album) = chatViewModel.getPreviewImageList(true, message)
                previewState.enterPreview(
                    key = MediaViewerKey(album = MediaViewerAlbum(album), initialIndex = startIndex),
                    radius = with(density) { 10.dp.toPx() },
                )
            }
        }

    val nav = LocalAppNavController.current

    ChatScaffold(
        panelState,
        modifier =
            Modifier
                .fillMaxSize()
                .hideKeyboardOnClickOutside(),
        applyStatusBar = false,
        topBar = {

        },
        bottomBar = {
            ChatGroupBottomBar(panelState, textFieldValue, onAction, modifier = Modifier.background(Color.White))
        },
        panelContent = {
            C2CBottomPanel(panelState, textFieldValue, null, onAction)
        },
        overlayContent = {
            AudioCenterStatusWidget()
            giftViewModel.GiftEffectView()
        },
        messageContent = {
            Box(modifier = Modifier.fillMaxSize()) {
                Column(modifier = modifier.fillMaxSize()) {
                    ApplyTips(chatGroupBean.applyJoinCount) {
                        nav.push(ChatGroupRoute.ChatGroupApplyJoinList(chatGroupBean.id))
                    }
                    Box(
                        Modifier
                            .fillMaxWidth()
                            .weight(1f)
                            .background(Color(0xFFF5F7F9)),
                    ) {
                        LazyColumn(
                            modifier =
                                Modifier
                                    .withAutoHidePanel(panelState)
                                    .fillMaxSize()
                                    .overScrollVertical(),
                            state = listState,
                            reverseLayout = true,
                            verticalArrangement = Arrangement.spacedBy(16.dp),
                            flingBehavior = rememberOverscrollFlingBehavior { listState },
                        ) {
                            items(chatViewModel.messageList, contentType = { item ->
                                item::class.simpleName
                            }) { item ->
                                val messageEntry = item.uiEntry
                                when (messageEntry) {
                                    is ChatGroupMsgEntry -> {
                                        when (messageEntry) {
                                            is ChatGroupMsgEntry.SystemTip -> {
                                                ChatGroupSystemTip(
                                                    modifier =
                                                        Modifier.noEffectClick {
                                                            LogUtils.d(item.message.toString())
                                                        },
                                                    messageEntry.avatar,
                                                    messageEntry.content,
                                                )
                                            }
                                            // Image
                                            is ChatGroupMsgEntry.GroupImage -> {
                                                messageEntry.Content(
                                                    previewState = previewState,
                                                    entry = item,
                                                    onAction = onAction,
                                                    roleState = roleState,
                                                    onAvatarClick = {
                                                        onCheckUser(item.user)
                                                    },
                                                )
                                            }

                                            // Text
                                            is ChatGroupMsgEntry.GroupText -> {
                                                ChatGroupItemWrapper(item, onAction, roleState, onAvatarClick = {
                                                    onCheckUser(item.user)
                                                }) {
                                                    MessageThemeBubble(
                                                        item,
                                                        bubbleColor = Color.White,
                                                        bubbleShape = bubbleShape,
                                                    ) {
                                                        Text(
                                                            messageEntry.data.text,
                                                            fontSize = 14.sp,
                                                            lineHeight = 20.sp,
                                                        )
                                                    }
                                                }
                                            }

                                            // Voice
                                            is ChatGroupMsgEntry.GroupVoice ->
                                                messageEntry.content.Render(
                                                    userInfoWrapper = @Composable { content ->
                                                        ChatGroupItemWrapper(
                                                            item,
                                                            onAction,
                                                            roleState,
                                                            onAvatarClick = {
                                                                onCheckUser(item.user)
                                                            },
                                                            content,
                                                        )
                                                    },
                                                    bubbleWrapper = @Composable {
                                                        MessageThemeBubble(entry = item, bubbleShape = bubbleShape) { it() }
                                                    },
                                                    onAction = onAction,
                                                )

                                            is ChatGroupMsgEntry.Unknown ->
                                                Text(
                                                    "Unknown:${messageEntry.text}",
                                                    color = WakooGrayText,
                                                    fontSize = 12.sp,
                                                    modifier =
                                                        Modifier
                                                            .fillMaxWidth()
                                                            .padding(horizontal = 16.dp)
                                                            .clickable(onClick = {
                                                                val msg = item.message
                                                                if (msg is UCCustomMessage) {
                                                                    LogUtils.d("$msg")
                                                                }
                                                            })
                                                            .background(Color.White, RoundedCornerShape(8.dp))
                                                            .padding(8.dp),
                                                )

                                            is ChatGroupMsgEntry.Notice ->
                                                with(messageEntry.groupNotice) {
                                                    GroupNoticeCard(this) {
                                                        val link = this.actionLink
                                                        if (actionType == 2) {
                                                            // 群公告
                                                            showGroupNoticeEditDialog()
                                                        }
                                                    }
                                                }

                                            is ChatGroupMsgEntry.Gift -> {
                                                val msg = messageEntry.giftMsg
                                                val giftWrapper = msg.gift
                                                ChatGroupItemWrapper(item, onAction, roleState, {
                                                    onCheckUser(item.user)
                                                }) {
                                                    ChatGroupGiftContent(
                                                        giftWrapper.gift.icon,
                                                        messageEntry.messageReceiver,
                                                        messageEntry.messageGift,
                                                        msg.sender == SelfUser?.id,
                                                        giftWrapper.receivers.size > 1,
                                                    )
                                                }
                                            }

                                            is ChatGroupMsgEntry.GroupRichText -> {
                                                Box(
                                                    modifier = Modifier
                                                        .fillMaxWidth()
                                                        .click(onClick = {
                                                            val t = messageEntry.richTextList
                                                            LogUtils.d("messages:$t")
                                                        }), contentAlignment = Alignment.Center
                                                ) {
                                                    EntryRichText(
                                                        messageEntry.richTextList,
                                                        fontSize = 12.sp,
                                                        lineHeight = 18.sp,
                                                        textAlign = TextAlign.Center,
                                                        modifier =
                                                            Modifier
                                                                .widthIn(max = 320.dp)
                                                                .background(Color(0xFFEBEEF4), RoundedCornerShape(6.dp))
                                                                .noEffectClick(onClick = {
                                                                    LogUtils.d(messageEntry.richTextList.toString())
                                                                })
                                                                .padding(4.dp),
                                                    )
                                                }
                                            }
                                        }
                                    }

                                    is TimeMsgEntry -> {
                                        messageEntry.C2CContent()
                                    }

                                    else ->
                                        Text(
                                            "$messageEntry",
                                            modifier =
                                                Modifier
                                                    .fillMaxWidth()
                                                    .padding(horizontal = 16.dp)
                                                    .clickable(onClick = {
                                                        val msg = item.message
                                                        if (msg is UCCustomMessage) {
                                                            LogUtils.d("$msg")
                                                        }
                                                    })
                                                    .background(Color.White, RoundedCornerShape(12.dp)),
                                        )
                                }
                            }

                            if (isLoading) {
                                item(key = "isLoading", contentType = "isLoading") {
                                    Box(
                                        modifier =
                                            Modifier
                                                .padding(top = 10.dp)
                                                .fillMaxWidth(),
                                        contentAlignment = Alignment.Center,
                                    ) {
                                        CircularProgressIndicator(
                                            modifier = Modifier.size(16.dp),
                                            strokeWidth = 1.5.dp,
                                        )
                                    }
                                }
                            }
                            item {
                                Spacer(Modifier.height(12.dp))
                            }
                        }
                    }
                }
                val self = LocalSelfUserProvider.current
                if (self.isCN) {
                    // 任务挂件
                    FloatingLayoutManager {
                        val floatingState =
                            rememberDraggableFloatingState(
                                initialAlignment = Alignment.BottomEnd,
                                initialOffsetDp = DpOffset(x = 0.dp, y = (-16).dp),
                                initialIsVisible = false,
                                initialIsStickyToEdge = true,
                                allowDragOutOfBounds = true,
                            )
                        LaunchedEffect(floatingState, chatGroupBean.boxVisible) {
                            floatingState.isVisible = chatGroupBean.boxVisible
                        }
                        DraggableItem(floatingState) {
                            GroupTaskPendant(
                                chatGroupBean.boxRedDotVisible,
                                modifier =
                                    Modifier
                                        .click(onClick = {
                                            dialogController.easyPostBottomPanel(
                                                dialogProperties = AnyPopDialogProperties(useSystemDialog = false),
                                            ) {
                                                GroupTaskDialogContent(boxVM, onRefresh = onRefresh, onDismiss = {
                                                    dismiss()
                                                })
                                            }
                                        }),
                            )
                        }
                    }
                }

                val unreadMsgInfo = chatViewModel.unreadMsgInfo
                val unreadAtInfo = chatViewModel.unreadAtInfo

                if (unreadMsgInfo != null || unreadAtInfo != null) {
                    Column(modifier = Modifier.align(Alignment.BottomEnd), horizontalAlignment = Alignment.End) {
                        val shape =
                            remember {
                                RoundedCornerShape(topStartPercent = 50, bottomStartPercent = 50)
                            }

                        if (unreadMsgInfo != null) {
                            val unreadMessageCount = unreadMsgInfo.unreadCount
                            val text =
                                "%s未读".localizedFormat(
                                    if (unreadMessageCount > 99) {
                                        "99+"
                                    } else {
                                        unreadMessageCount
                                    },
                                )
                            Text(
                                text = text,
                                modifier =
                                    Modifier
                                        .background(Color(0xFFEBEEF4), shape)
                                        .padding(8.dp, 2.dp)
                                        .clickable(onClick = { chatViewModel.scrollToUnreadMessage() }),
                                color = Color(0xFF666666),
                                fontSize = 12.sp,
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                        }

                        if (unreadAtInfo != null) {
                            Text(
                                text = "有人@你".localized,
                                modifier =
                                    Modifier
                                        .background(Color(0xFFC8FFC9), shape)
                                        .padding(8.dp, 2.dp)
                                        .clickable(onClick = {
                                            chatViewModel.scrollToAtMessage()
                                        }),
                                color = Color(0xFF1A7D1D),
                                fontSize = 12.sp,
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                        }
                        Spacer(modifier = Modifier.height(20.dp))
                    }
                }
            }
        },
    )
}

@Composable
fun ApplyTips(
    applyCount: Int,
    onClick: OnAction = {},
) {
    if (applyCount > 0) {
        Row(
            modifier =
                Modifier
                    .padding(16.dp, 8.dp)
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(6.dp))
                    .clickable(onClick = onClick)
                    .background(colorSurface)
                    .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(WakooIcons.Admin, "icon", tint = colorOnSurface)
            SizeWidth(2.dp)
            Text("%s条加入群组申请消息待处理".localizedFormat(applyCount))
            Weight()
            Badge(containerColor = Color(0xFFF53F3F), contentColor = Color.White, modifier = Modifier.padding(4.dp)) {
                Text(if (applyCount < 99) applyCount.toString() else "99+", fontSize = 12.sp)
            }
            Icon(Icons.AutoMirrored.Filled.KeyboardArrowRight, "arrow", tint = colorOnSurface)
        }
    }
}

@Composable
fun ChatGroupBottomBar(
    panelState: KeyboardPanelState,
    textFieldValue: MutableState<TextFieldValue>,
    onAction: IC2CAction,
    modifier: Modifier = Modifier,
    focusRequester: FocusRequester? = null,
) {
    Column(
        modifier =
            modifier.fillMaxWidth(),
    ) {
        C2CBottomBar(panelState, textFieldValue, showGiftButton = true, onAction = onAction)
    }
}

@Preview
@Composable
private fun ApplyTipsPreview() {
    WakooTheme {
        ApplyTips(99)
    }
}
