@file:Suppress("UNCHECKED_CAST")

package com.buque.wakoo.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.Saver
import androidx.compose.runtime.saveable.mapSaver
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.navigation3.runtime.rememberNavBackStack
import androidx.savedstate.SavedState
import androidx.savedstate.serialization.SavedStateConfiguration
import androidx.savedstate.serialization.decodeFromSavedState
import androidx.savedstate.serialization.encodeToSavedState

object LocalAppNavController {
    private val controllers = mutableStateMapOf<CtrlKey<*>, AppNavController>()

    private val LocalAppNavController =
        staticCompositionLocalOf<AppNavController> {
            error("CompositionLocal LocalAppNavController not present")
        }

    private val LocalRootNavController =
        staticCompositionLocalOf<RootNavController> {
            error("CompositionLocal LocalAppNavController not present")
        }

    val current: AppNavController
        @Composable
        get() = LocalAppNavController.current

    val root: RootNavController
        @Composable
        get() = LocalRootNavController.current

    val useGlobalRoot: RootNavController?
        get() = get(RootNavCtrlKey)

    val useRoot: RootNavController?
        get() = useGlobalRoot?.takeIf { it.isLoggedIn }

    operator fun <C : AppNavController> get(key: CtrlKey<C>): C? = controllers[key] as C?

    @Composable
    fun ProvideController(
        controller: AppNavController,
        content: @Composable () -> Unit,
    ) {
        // 使用 remember 确保 controller 实例在重组中保持不变
        // 或者如果 controller 是在外部创建并传入的，确保其也是remembered的
        val controllerKey = controller.key
        // 使用 DisposableEffect 在 Composable 进入和离开 Composition 时执行操作
        DisposableEffect(controllerKey) {
            // key 是 controllerKey，当 controllerKey 变化时（不应该变化），或Composable离开composition时触发
            // 进入 Composition (页面或控制器被提供时)
            if (controllers.isEmpty() && !controllerKey.isRoot) {
                throw IllegalStateException("Root controller must be provided first")
            }
//            if (controllers.contains(controllerKey)) {
//                throw IllegalStateException("Controller for key ${controller.key} already provided")
//            }
            controllers[controllerKey] = controller
            onDispose {
                controllers.remove(controllerKey)
            }
        }

        val values =
            remember(controller) {
                buildList {
                    add(LocalAppNavController provides controller)
                    if (controller.isRoot) {
                        add(LocalRootNavController provides controller as RootNavController)
                    }
                }.toTypedArray()
            }

        // 提供 CompositionLocal 给子 Composable
        CompositionLocalProvider(*values) {
            content()
        }
    }
}

private val saverAny: Saver<Any, SavedState> =
    Saver(
        save = { original ->
            encodeToSavedState(
                UnsafePolymorphicSerializer(),
                original,
                SavedStateConfiguration.DEFAULT,
            )
        },
        restore = { savedState ->
            decodeFromSavedState(
                UnsafePolymorphicSerializer(),
                savedState,
                SavedStateConfiguration.DEFAULT,
            )
        },
    )

/**
 * @param key 每一个不同类型的导航控制器都有一个唯一的key，参考[RootNavCtrlKey]
 */
@Composable
fun rememberAppNavController(
    key: CtrlKey<*>,
    vararg elements: AppNavKey,
): AppNavController {
    val backStack = rememberNavBackStack(*elements) as AppNavBackStack
    return rememberSaveable(
        inputs = arrayOf(key, backStack),
        saver =
            mapSaver(
                save = { controller ->
                    saverAny.run {
                        buildMap {
                            for ((key, value) in controller.pendingResults) {
                                val saveable =
                                    try {
                                        save(value)
                                    } catch (_: Exception) {
                                        null
                                    }
                                if (saveable != null) {
                                    put(key, saveable)
                                }
                            }
                        }
                    }
                },
                restore = { map ->
                    AppNavController(
                        key = key,
                        defaultBackStack = backStack,
                        initialPendingResults =
                            buildMap {
                                for ((key, value) in map) {
                                    if (value is SavedState) {
                                        val original = saverAny.restore(value)
                                        if (original != null) {
                                            put(key, original)
                                        }
                                    }
                                }
                            },
                    )
                },
            ),
    ) {
        // 这个 lambda 只在没有可恢复的状态时（即首次创建时）执行。
        AppNavController(
            key = key,
            defaultBackStack = backStack,
        )
    }
}

/**
 * @param key 每一个不同类型的导航控制器都有一个唯一的key，参考[RootNavCtrlKey]
 */
@Composable
inline fun <reified T : BottomNavKey<*>> rememberBottomNavController(
    key: CtrlKey<*>,
    vararg elements: T,
): BottomNavController<T> {
    val backStack = rememberNavBackStack<T>(*elements) as AppNavBackStack
    return remember(
        key,
        backStack,
    ) {
        BottomNavController(
            key,
            backStack,
        )
    }
}

@Composable
fun rememberRootNavController(
    loginNavKey: AppNavKey,
    isLoggedIn: Boolean = false,
    vararg elements: AppNavKey,
): RootNavController {
    val backStackWithNotLoggedIn = rememberNavBackStack(*elements) as AppNavBackStack

    val backStackWithLoggedIn = rememberNavBackStack<AppNavKey>(Route.Home) as AppNavBackStack
    return rememberSaveable(
        backStackWithNotLoggedIn,
        backStackWithLoggedIn,
        saver =
            mapSaver(
                save = { controller ->
                    saverAny.run {
                        buildMap {
                            put("onLoginSuccessNavKey", controller.onLoginSuccessNavKey?.let { save(it) })
                            for ((key, value) in controller.pendingResults) {
                                val saveable =
                                    try {
                                        save(value)
                                    } catch (_: Exception) {
                                        null
                                    }
                                if (saveable != null) {
                                    put(key, saveable)
                                }
                            }
                        }
                    }
                },
                restore = { map ->
                    RootNavController(
                        loginNavKey = loginNavKey,
                        backStackWithNotLoggedIn = backStackWithNotLoggedIn,
                        backStackWithLoggedIn = backStackWithLoggedIn,
                        initialPendingResults =
                            buildMap {
                                for ((key, value) in map) {
                                    if (key != "onLoginSuccessNavKey" && value is SavedState) {
                                        val original = saverAny.restore(value)
                                        if (original != null) {
                                            put(key, original)
                                        }
                                    }
                                }
                            },
                        isLoggedIn = isLoggedIn,
                        loginSuccessNavKey =
                            map["onLoginSuccessNavKey"]
                                ?.takeIf {
                                    it is SavedState
                                }?.let { saverAny.restore(it as SavedState) } as? AppNavKey?,
                    )
                },
            ),
    ) {
        RootNavController(
            loginNavKey = loginNavKey,
            backStackWithNotLoggedIn = backStackWithNotLoggedIn,
            backStackWithLoggedIn = backStackWithLoggedIn,
            isLoggedIn = isLoggedIn,
        )
    }
}
